'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { useState } from 'react'
import { Eye, EyeOff } from 'lucide-react'
import { motion } from 'framer-motion'
import Image from 'next/image'
import { useTenantTheme } from '@/src/hooks/tenant/use-tenant-theme'
import { ResetPasswordConfirmFormData, resetPasswordConfirmSchema } from './schema'

interface ResetPasswordConfirmFormProps {
  onSubmit: (data: ResetPasswordConfirmFormData) => Promise<void>
  isLoading: boolean
  errorMessage?: string | null
  userEmail?: string
}

export const ResetPasswordConfirmForm = ({
  onSubmit,
  isLoading,
  errorMessage,
  userEmail
}: ResetPasswordConfirmFormProps) => {
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const { logoUrl } = useTenantTheme()

  const form = useForm<ResetPasswordConfirmFormData>({
    resolver: zodResolver(resetPasswordConfirmSchema),
    defaultValues: {
      password: '',
      confirmPassword: '',
    },
  })

  const handleFormSubmit = async (data: ResetPasswordConfirmFormData) => {
    await onSubmit(data)
  }

  return (
    <div className="w-full max-w-lg space-y-8 rounded-2xl bg-white p-8 shadow-xl shadow-gray-200/20 ring-1 ring-gray-200 dark:bg-gray-900 dark:shadow-none dark:ring-gray-800">
      <div className="text-center">
        <motion.div
          initial={{ scale: 0.5, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Image
            src={logoUrl || "/logo.svg"}
            alt="Logo Academia"
            width={112}
            height={112}
            priority
            className="mx-auto h-28 w-28 object-contain"
          />
        </motion.div>

        <motion.h2
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="mt-6 bg-gradient-to-r tenant-primary bg-clip-text text-3xl font-bold tracking-tight text-transparent dark:from-white dark:to-gray-200"
        >
          Redefinir Senha
        </motion.h2>
        <motion.p
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="mt-2 text-sm text-gray-600 dark:text-gray-400"
        >
          {userEmail ? (
            <>Defina uma nova senha para <strong>{userEmail}</strong></>
          ) : (
            'Defina sua nova senha'
          )}
        </motion.p>
      </div>

      {errorMessage && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="rounded-lg bg-red-50 p-4 dark:bg-red-900/50"
        >
          <p className="text-sm font-medium text-red-800 dark:text-red-200">
            {errorMessage}
          </p>
        </motion.div>
      )}

      <form onSubmit={form.handleSubmit(handleFormSubmit)} className="mt-8 space-y-6">
        <div className="space-y-5 rounded-md">
          <div>
            <label
              htmlFor="password"
              className="block text-sm font-medium text-gray-900 dark:text-gray-200"
            >
              Nova Senha
            </label>
            <div className="mt-1.5">
              <div className="relative">
                <input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  placeholder="Digite sua nova senha"
                  {...form.register('password')}
                  disabled={isLoading}
                  className={`block w-full rounded-lg border ${form.formState.errors.password ? 'border-red-500' : 'border-gray-200'} bg-white px-4 py-3 text-gray-900 transition-colors placeholder:text-gray-500 focus:tenant-primary-border focus:outline-none focus:ring-4 focus:tenant-primary-border/10 dark:border-gray-800 dark:bg-gray-900 dark:text-white dark:placeholder:text-gray-400 dark:focus:tenant-primary-border dark:focus:tenant-primary-border/10`}
                  aria-invalid={!!form.formState.errors.password}
                />
                <button
                  type="button"
                  className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 transition-colors hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                  onClick={() => setShowPassword(!showPassword)}
                  disabled={isLoading}
                >
                  {showPassword ? (
                    <EyeOff className="h-5 w-5" />
                  ) : (
                    <Eye className="h-5 w-5" />
                  )}
                </button>
              </div>
              {form.formState.errors.password && (
                <p className="mt-1.5 text-sm tenant-primary dark:tenant-primary">
                  {form.formState.errors.password.message}
                </p>
              )}
            </div>
          </div>

          <div>
            <label
              htmlFor="confirmPassword"
              className="block text-sm font-medium text-gray-900 dark:text-gray-200"
            >
              Confirmar Nova Senha
            </label>
            <div className="mt-1.5">
              <div className="relative">
                <input
                  id="confirmPassword"
                  type={showConfirmPassword ? 'text' : 'password'}
                  placeholder="Confirme sua nova senha"
                  {...form.register('confirmPassword')}
                  disabled={isLoading}
                  className={`block w-full rounded-lg border ${form.formState.errors.confirmPassword ? 'border-red-500' : 'border-gray-200'} bg-white px-4 py-3 text-gray-900 transition-colors placeholder:text-gray-500 focus:tenant-primary-border focus:outline-none focus:ring-4 focus:tenant-primary-border/10 dark:border-gray-800 dark:bg-gray-900 dark:text-white dark:placeholder:text-gray-400 dark:focus:tenant-primary-border dark:focus:tenant-primary-border/10`}
                  aria-invalid={!!form.formState.errors.confirmPassword}
                />
                <button
                  type="button"
                  className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 transition-colors hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  disabled={isLoading}
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-5 w-5" />
                  ) : (
                    <Eye className="h-5 w-5" />
                  )}
                </button>
              </div>
              {form.formState.errors.confirmPassword && (
                <p className="mt-1.5 text-sm tenant-primary dark:tenant-primary">
                  {form.formState.errors.confirmPassword.message}
                </p>
              )}
            </div>
          </div>
        </div>

        <div>
          <button
            type="submit"
            className="relative w-full rounded-lg tenant-primary-bg px-4 py-3 text-sm font-semibold text-white transition-all before:absolute before:inset-0 before:rounded-lg before:bg-transparent/0 before:transition-colors hover:before:bg-white/10 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:tenant-primary-border disabled:cursor-not-allowed disabled:opacity-50"
            disabled={isLoading}
          >
            {isLoading ? (
              <span className="flex items-center justify-center gap-2">
                <svg className="h-4 w-4 animate-spin" viewBox="0 0 24 24">
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                    fill="none"
                  />
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  />
                </svg>
                Atualizando...
              </span>
            ) : (
              'Atualizar Senha'
            )}
          </button>
        </div>
      </form>
    </div>
  )
}
