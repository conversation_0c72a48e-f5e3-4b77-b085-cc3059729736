'use client';

import { useQuery } from '@tanstack/react-query';
import { CACHE_KEYS } from '@/constants/cache-keys';
import { getBeltLevelsForModality } from '@/app/(dashboard)/perfil/actions/graduation-actions';
import { GraduationLevel } from '@/services/belts/levels';

/**
 * Hook para buscar níveis de faixa para uma modalidade específica
 * Usa React Query para cache e evita recarregamentos desnecessários
 */
export function useBeltLevels(modalitySlug: string | null) {
  return useQuery<GraduationLevel[]>({
    queryKey: modalitySlug ? CACHE_KEYS.BELTS.LEVELS(modalitySlug) : ['belt-levels', null],
    queryFn: async () => {
      if (!modalitySlug) {
        return [];
      }

      const result = await getBeltLevelsForModality(modalitySlug);
      
      if (!result.success || !result.data) {
        throw new Error(result.errors?._form || 'Erro ao carregar níveis de faixa');
      }
      
      return result.data;
    },
    enabled: !!modalitySlug,
    staleTime: 1000 * 60 * 15, // 15 minutos - níveis de faixa mudam raramente
    gcTime: 1000 * 60 * 45, // 45 minutos no cache
    retry: 2,
    refetchOnWindowFocus: false,
  });
}