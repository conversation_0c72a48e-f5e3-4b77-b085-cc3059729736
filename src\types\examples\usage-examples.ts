/**
 * Exemplos de uso dos tipos do sistema de histórico de status
 * 
 * Este arquivo contém exemplos práticos de como utilizar os tipos
 * definidos para o sistema de histórico de status de usuários.
 * 
 * NOTA: Este arquivo é apenas para documentação e não deve ser
 * importado em código de produção.
 */

import {
    UserStatusHistory,
    StatusChangeParams,
    StatusHistoryQuery,
    StudentsMetricsWithHistory,
    DailyUsersChartDataWithHistory,
    validateStatusChange,
    formatStatusForDisplay,
    getStatusColor,
    isValidUserStatus
} from '../index'

/**
 * Exemplo 1: Criando um registro de histórico de status
 */
function exemploRegistroHistorico(): UserStatusHistory {
    return {
        id: 'hist_123456789',
        tenant_id: 'tenant_abc123',
        branch_id: 'branch_def456',
        user_id: 'user_ghi789',
        old_status: 'inactive',
        new_status: 'active',
        changed_at: '2024-01-15T10:30:00.000Z',
        changed_by: 'admin_user_123',
        reason: 'Reativação após pagamento da mensalidade',
        metadata: {
            source: 'manual',
            payment_id: 'pay_xyz789',
            previous_updated_at: '2024-01-10T15:20:00.000Z',
            change_source: 'admin_panel'
        },
        created_at: '2024-01-15T10:30:00.000Z'
    }
}

/**
 * Exemplo 2: Parâmetros para mudança de status
 */
function exemploMudancaStatus(): StatusChangeParams {
    return {
        user_id: 'user_ghi789',
        new_status: 'suspended',
        changed_by: 'admin_user_123',
        reason: 'Suspensão por inadimplência',
        metadata: {
            suspension_type: 'payment_overdue',
            overdue_days: 15,
            last_payment_date: '2023-12-01',
            notification_sent: true
        }
    }
}

/**
 * Exemplo 3: Consulta de histórico com filtros
 */
function exemploConsultaHistorico(): StatusHistoryQuery {
    return {
        tenant_id: 'tenant_abc123',
        branch_id: 'branch_def456',
        start_date: '2024-01-01',
        end_date: '2024-01-31',
        status: 'active',
        limit: 50,
        offset: 0
    }
}

/**
 * Exemplo 4: Métricas de estudantes com histórico
 */
function exemploMetricasComHistorico(): StudentsMetricsWithHistory {
    return {
        active_users_now: 150,
        active_users_last_month: 142,
        new_users_now: 12,
        new_users_last_month: 8,
        suspended_users_now: 5,
        inactive_users_now: 23,
        reference_date: '2024-01-31',
        is_history_based: true
    }
}

/**
 * Exemplo 5: Dados para gráfico diário
 */
function exemploDadosGraficoDiario(): DailyUsersChartDataWithHistory[] {
    return [
        {
            day_date: '01/01',
            full_date: '2024-01-01',
            active_count: 145,
            inactive_count: 20,
            suspended_count: 3,
            total_count: 168,
            status_changes_count: 2
        },
        {
            day_date: '02/01',
            full_date: '2024-01-02',
            active_count: 147,
            inactive_count: 19,
            suspended_count: 3,
            total_count: 169,
            status_changes_count: 3
        }
        // ... mais dados
    ]
}

/**
 * Exemplo 6: Validação de mudança de status
 */
function exemploValidacaoMudanca() {
    // Validar mudança de ativo para suspenso (válida)
    const validacao1 = validateStatusChange('active', 'suspended')
    console.log('Mudança ativo -> suspenso:', validacao1)
    // Resultado: { is_valid: true, message: "Suspensão de usuário ativo" }

    // Validar mudança de ativo para ativo (inválida)
    const validacao2 = validateStatusChange('active', 'active')
    console.log('Mudança ativo -> ativo:', validacao2)
    // Resultado: { is_valid: false, message: "Usuário já está ativo", error_code: "TRANSITION_NOT_ALLOWED" }
}

/**
 * Exemplo 7: Formatação de status para exibição
 */
function exemploFormatacaoStatus() {
    const statuses: Array<'active' | 'inactive' | 'suspended' | null> = [
        'active', 'inactive', 'suspended', null
    ]

    statuses.forEach(status => {
        const displayText = formatStatusForDisplay(status)
        const color = getStatusColor(status)

        console.log(`Status: ${status} -> Display: ${displayText}, Cor: ${color}`)
    })

    // Resultado:
    // Status: active -> Display: Ativo, Cor: #10b981
    // Status: inactive -> Display: Inativo, Cor: #6b7280
    // Status: suspended -> Display: Suspenso, Cor: #f59e0b
    // Status: null -> Display: Não definido, Cor: #6b7280
}

/**
 * Exemplo 8: Verificação de tipo com type guard
 */
function exemploTypeGuard() {
    const valores = ['active', 'invalid', 'suspended', 123, null]

    valores.forEach(valor => {
        if (isValidUserStatus(valor)) {
            // TypeScript agora sabe que 'valor' é do tipo UserStatus
            console.log(`Status válido: ${valor}`)
            const cor = getStatusColor(valor) // Sem erro de tipo
        } else {
            console.log(`Valor inválido: ${valor}`)
        }
    })
}

/**
 * Exemplo 9: Uso em componente React (pseudo-código)
 */
/*
function ComponenteHistoricoStatus({ userId }: { userId: string }) {
  const [historico, setHistorico] = useState<UserStatusHistory[]>([])
  const [loading, setLoading] = useState(true)
  
  useEffect(() => {
    async function carregarHistorico() {
      try {
        const query: StatusHistoryQuery = {
          tenant_id: 'tenant_abc123',
          user_id: userId,
          limit: 20
        }
        
        const response = await buscarHistoricoStatus(query)
        setHistorico(response.data)
      } catch (error) {
        console.error('Erro ao carregar histórico:', error)
      } finally {
        setLoading(false)
      }
    }
    
    carregarHistorico()
  }, [userId])
  
  if (loading) return <div>Carregando...</div>
  
  return (
    <div>
      <h3>Histórico de Status</h3>
      {historico.map(registro => (
        <div key={registro.id} className="status-change">
          <span style={{ color: getStatusColor(registro.new_status) }}>
            {formatStatusForDisplay(registro.new_status)}
          </span>
          <span>{formatDateTimeToBrazilTimezone(registro.changed_at)}</span>
          {registro.reason && <span>{registro.reason}</span>}
        </div>
      ))}
    </div>
  )
}
*/

/**
 * Exemplo 10: Uso em action do Next.js (pseudo-código)
 */
/*
export async function alterarStatusUsuario(
  params: StatusChangeParams
): Promise<{ success: boolean; message: string }> {
  try {
    // Validar a mudança antes de executar
    const statusAtual = await obterStatusAtualUsuario(params.user_id)
    const validacao = validateStatusChange(statusAtual, params.new_status)
    
    if (!validacao.is_valid) {
      return {
        success: false,
        message: validacao.message
      }
    }
    
    // Executar a mudança (será capturada pelo trigger)
    await supabase
      .from('users')
      .update({ 
        status: params.new_status,
        metadata: {
          ...metadata_existente,
          status_change_reason: params.reason
        }
      })
      .eq('id', params.user_id)
    
    return {
      success: true,
      message: 'Status alterado com sucesso'
    }
  } catch (error) {
    return {
      success: false,
      message: 'Erro interno do servidor'
    }
  }
}
*/

export {
    exemploRegistroHistorico,
    exemploMudancaStatus,
    exemploConsultaHistorico,
    exemploMetricasComHistorico,
    exemploDadosGraficoDiario,
    exemploValidacaoMudanca,
    exemploFormatacaoStatus,
    exemploTypeGuard
}