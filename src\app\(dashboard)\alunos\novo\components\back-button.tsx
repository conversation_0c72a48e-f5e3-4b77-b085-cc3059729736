"use client";

import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";

export default function BackButton() {
  const router = useRouter();

  const handleBack = () => {
    router.back();
  };

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={handleBack}
      className="flex items-center gap-1 text-white-600 hover:text-white-900 p-0 h-auto font-normal mb-2"
    >
      <ArrowLeft className="h-4 w-4" />
      Voltar
    </Button>
  );
}