# Troubleshooting: Geração de Senhas e Notificações

Guia para solucionar problemas comuns relacionados à geração de senhas e envio de credenciais por e-mail.

## Problemas Comuns

### 1. Senha não é gerada

#### Sintomas
- <PERSON><PERSON>: "Não foi possível gerar senha que atenda aos critérios"
- Aluno criado com senha padrão "123456789"
- Log: "Erro ao gerar senha, usando senha padrão"

#### Causas Possíveis
- Configuração muito restritiva
- Problema com `crypto.getRandomValues()`
- Ambiente não suporta Web Crypto API

#### Soluções

**1. Verificar configuração:**
```typescript
// ❌ Muito restritivo
const result = generatePassword({
  length: 4,
  includeUppercase: false,
  includeLowercase: false,
  includeNumbers: false,
  includeSpecialChars: false
});

// ✅ Configuração válida
const result = generatePassword({
  length: 8,
  includeUppercase: true,
  includeLowercase: true,
  includeNumbers: true
});
```

**2. Verificar suporte do navegador:**
```typescript
// Verificar se crypto está disponível
if (typeof crypto === 'undefined' || !crypto.getRandomValues) {
  console.error('Web Crypto API não suportada');
  // Implementar fallback
}
```

**3. Usar fallback manual:**
```typescript
try {
  const result = generatePasswordSync();
  return result.password;
} catch (error) {
  // Fallback para geração manual
  return generateFallbackPassword();
}
```

### 2. E-mail de credenciais não é enviado

#### Sintomas
- Aluno criado com sucesso
- Mensagem: "mas houve erro ao enviar credenciais por e-mail"
- Log: "Falha ao enviar credenciais por e-mail"

#### Causas Possíveis
- Configuração de e-mail incorreta
- E-mail do aluno inválido
- Provedor de e-mail bloqueando
- Timeout na requisição

#### Soluções

**1. Verificar configuração do tenant:**
```sql
-- Verificar se e-mail está habilitado
SELECT email_enabled, notification_types 
FROM tenant_notification_settings 
WHERE tenant_id = 'seu-tenant-id';
```

**2. Verificar logs detalhados:**
```typescript
// Ativar logs de debug
console.log('🔍 [DEBUG] Dados do e-mail:', {
  tenantId,
  userId,
  studentEmail,
  hasPassword: !!temporaryPassword
});
```

**3. Testar envio manual:**
```typescript
// Testar dispatcher diretamente
const dispatcher = new NotificationDispatcher();
const result = await dispatcher.sendCredentials({
  tenantId: 'test-tenant',
  userId: 'test-user',
  studentName: 'Teste',
  studentEmail: '<EMAIL>',
  temporaryPassword: 'TestPass123!'
});
console.log('Resultado:', result);
```

### 3. Template de e-mail não renderiza

#### Sintomas
- E-mail enviado mas aparece em branco
- Erro: "Template não encontrado"
- HTML malformado no e-mail

#### Causas Possíveis
- Props obrigatórias faltando
- Erro na renderização do React Email
- Template não registrado corretamente

#### Soluções

**1. Verificar props obrigatórias:**
```typescript
// Verificar se todas as props estão presentes
const requiredProps = [
  'academyName',
  'studentName', 
  'studentEmail',
  'temporaryPassword',
  'loginUrl'
];

const missingProps = requiredProps.filter(prop => !templateData[prop]);
if (missingProps.length > 0) {
  console.error('Props faltando:', missingProps);
}
```

**2. Testar renderização isolada:**
```typescript
import { render } from '@react-email/render';
import { CredentialsTemplate } from './templates/credentials-template';

try {
  const html = await render(CredentialsTemplate({
    academyName: 'Test',
    studentName: 'Test',
    studentEmail: '<EMAIL>',
    temporaryPassword: 'Test123!',
    loginUrl: 'https://test.com'
  }));
  console.log('Template renderizado com sucesso');
} catch (error) {
  console.error('Erro na renderização:', error);
}
```

### 4. Permissões de notificação

#### Sintomas
- Log: "Canal de e-mail não permitido"
- E-mail não enviado por restrições de permissão
- Configurações de notificação não aplicadas

#### Causas Possíveis
- Configurações de tenant restritivas
- Preferências do usuário bloqueando e-mail
- RLS (Row Level Security) bloqueando acesso

#### Soluções

**1. Verificar configurações do tenant:**
```sql
-- Verificar configurações globais
SELECT * FROM tenant_notification_settings 
WHERE tenant_id = 'seu-tenant-id';

-- Verificar configurações por tipo
SELECT notification_types 
FROM tenant_notification_settings 
WHERE tenant_id = 'seu-tenant-id';
```

**2. Verificar preferências do usuário:**
```sql
-- Verificar preferências de notificação
SELECT * FROM notification_preferences 
WHERE tenant_id = 'seu-tenant-id' 
AND user_id = 'seu-user-id';
```

**3. Forçar envio para credenciais:**
```typescript
// Credenciais são críticas - forçar envio
const permittedChannels = await this.permissionService.getPermittedChannels(
  tenantId, userId, 'system', ['email']
);

if (!permittedChannels.includes('email')) {
  console.warn('Forçando envio de credenciais mesmo sem permissão');
  permittedChannels.push('email');
}
```

### 5. Performance lenta

#### Sintomas
- Criação de aluno demora muito
- Timeout no envio de e-mail
- Interface trava durante geração

#### Causas Possíveis
- Geração de senha muito complexa
- Envio de e-mail síncrono
- Múltiplas tentativas de geração

#### Soluções

**1. Otimizar geração de senha:**
```typescript
// Usar configuração mais simples
const result = generatePasswordSync({
  length: 10,  // Menor
  includeSpecialChars: false,  // Menos complexo
  excludeSimilar: false  // Menos filtros
});
```

**2. Implementar timeout:**
```typescript
// Timeout para envio de e-mail
const timeoutPromise = new Promise((_, reject) => {
  setTimeout(() => reject(new Error('Timeout')), 30000);
});

const result = await Promise.race([
  dispatcher.sendCredentials(data),
  timeoutPromise
]);
```

**3. Tornar envio assíncrono:**
```typescript
// Não bloquear criação do usuário
try {
  // Enviar em background
  setImmediate(async () => {
    await dispatcher.sendCredentials(data);
  });
} catch (error) {
  // Log mas não falhar
  console.error('Erro no envio assíncrono:', error);
}
```

## Logs de Debug

### Ativar Logs Detalhados

```typescript
// No arquivo de configuração ou .env
DEBUG_NOTIFICATIONS=true
LOG_LEVEL=debug

// No código
if (process.env.DEBUG_NOTIFICATIONS) {
  console.log('🔍 [DEBUG] Detalhes da operação:', data);
}
```

### Logs Importantes para Monitorar

```typescript
// Geração de senha
console.log('✅ Senha gerada. Força:', result.strength);
console.log('❌ Erro na geração:', error.message);

// Envio de e-mail
console.log('📧 Iniciando envio de credenciais');
console.log('✅ Credenciais enviadas com sucesso');
console.log('❌ Falha no envio:', error.message);

// Permissões
console.log('🔒 Canais permitidos:', permittedChannels);
console.log('⚠️ Canal não permitido, forçando envio');
```

## Monitoramento

### Métricas Importantes

1. **Taxa de Sucesso de Geração de Senha**
   - Meta: > 99%
   - Alerta se < 95%

2. **Taxa de Entrega de E-mail**
   - Meta: > 95%
   - Alerta se < 90%

3. **Tempo de Resposta**
   - Geração de senha: < 100ms
   - Envio de e-mail: < 30s

### Queries de Monitoramento

```sql
-- Taxa de sucesso de criação de alunos
SELECT 
  DATE(created_at) as date,
  COUNT(*) as total_created,
  COUNT(CASE WHEN credentials_sent = true THEN 1 END) as credentials_sent,
  ROUND(
    COUNT(CASE WHEN credentials_sent = true THEN 1 END) * 100.0 / COUNT(*), 
    2
  ) as success_rate
FROM student_creation_logs 
WHERE created_at >= NOW() - INTERVAL '7 days'
GROUP BY DATE(created_at)
ORDER BY date DESC;
```

## Testes

### Teste de Geração de Senha

```typescript
describe('Password Generation', () => {
  test('should generate secure password', () => {
    const result = generatePasswordSync();
    
    expect(result.password).toHaveLength(12);
    expect(result.strength).toBe('strong');
    expect(result.entropy).toBeGreaterThan(60);
  });
  
  test('should handle invalid config', () => {
    expect(() => {
      generatePasswordSync({ length: 2 });
    }).toThrow('Comprimento mínimo');
  });
});
```

### Teste de Envio de E-mail

```typescript
describe('Credentials Email', () => {
  test('should send credentials successfully', async () => {
    const dispatcher = new NotificationDispatcher();
    
    const result = await dispatcher.sendCredentials({
      tenantId: 'test-tenant',
      userId: 'test-user',
      studentName: 'Test Student',
      studentEmail: '<EMAIL>',
      temporaryPassword: 'TestPass123!'
    });
    
    expect(result.success).toBe(true);
    expect(result.credentialsSent).toBe(true);
  });
});
```

## Contato para Suporte

Se os problemas persistirem após seguir este guia:

1. **Colete logs detalhados** com timestamps
2. **Documente passos para reproduzir** o problema
3. **Inclua informações do ambiente** (browser, OS, etc.)
4. **Teste em ambiente isolado** se possível

### Informações Úteis para Suporte

```typescript
// Informações do ambiente
const debugInfo = {
  userAgent: navigator.userAgent,
  cryptoSupported: typeof crypto !== 'undefined',
  nodeVersion: process.version,
  environment: process.env.NODE_ENV,
  timestamp: new Date().toISOString()
};

console.log('Debug Info:', debugInfo);
```