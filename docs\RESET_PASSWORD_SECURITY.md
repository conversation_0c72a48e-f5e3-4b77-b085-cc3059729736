# Sistema de Reset de Senha Seguro e Multi-Tenant

## Visão Geral

O sistema de reset de senha foi implementado com foco em segurança e isolamento entre academias (tenants). Cada academia possui seu próprio subdomínio e os usuários só podem redefinir senhas dentro do contexto da sua academia.

## Fluxo de Funcionamento

### 1. Solicitação de Reset
- Usuário acessa `/reset-password` no subdomínio da sua academia
- Insere email e solicita reset
- Sistema valida se o email pertence àquela academia

### 2. Envio de Email
- Edge Function `send-email` é acionada via webhook do Supabase Auth
- Busca dados do usuário e da academia (incluindo slug)
- Constrói URL específica da academia: `https://{slug}.domain.com/reset-password/confirm?token_hash={token}`
- Envia email personalizado com branding da academia

### 3. Confirmação e Nova Senha
- Usuário clica no link e é direcionado para `/reset-password/confirm`
- Sistema valida o token usando `supabase.auth.verifyOtp()`
- Apresenta formulário para definir nova senha
- Atualiza senha e força logout para novo login

## Segurança Implementada

### Isolamento por Academia
- **URL específica**: Cada academia tem seu próprio link de reset
- **Validação de tenant**: Middleware garante que usuário só acesse dados da sua academia
- **Token único**: Cada token só funciona para o usuário específico

### Validação de Token
- **Verificação server-side**: Token é validado no servidor antes de permitir acesso
- **Uso único**: Token é consumido após uso, impedindo reutilização
- **Expiração**: Tokens expiram automaticamente (configurável no Supabase)

### Proteção contra Ataques
- **CSRF**: Protegido pelos mecanismos do Next.js
- **Força bruta**: Rate limiting do Supabase Auth
- **Phishing**: URLs específicas por academia dificultam ataques genéricos

## Estrutura de Arquivos

```
src/
├── app/
│   ├── (auth-pages)/
│   │   └── reset-password/
│   │       ├── page.tsx              # Página de solicitação
│   │       └── confirm/
│   │           └── page.tsx          # Página de confirmação
│   └── api/
│       └── auth/
│           └── reset-password-confirm/
│               └── route.ts          # API para processar nova senha
├── components/
│   └── auth/
│       └── reset-password-confirm/
│           ├── index.tsx             # Componente principal
│           ├── form.tsx              # Formulário de nova senha
│           └── schema.ts             # Validação Zod
└── supabase/
    └── functions/
        └── send-email/
            └── index.ts              # Edge Function atualizada
```

## Configuração

### Variáveis de Ambiente
```env
NEXT_PUBLIC_BASE_DOMAIN=localhost:3000  # Para desenvolvimento
# ou
NEXT_PUBLIC_BASE_DOMAIN=yourdomain.com   # Para produção
```

### Webhook do Supabase
A Edge Function deve estar configurada como webhook para o evento `auth.recovery` no painel do Supabase.

## URLs Geradas

### Desenvolvimento Local
```
http://academia1.localhost:3000/reset-password/confirm?token_hash=abc123
http://academia2.localhost:3000/reset-password/confirm?token_hash=def456
```

### Produção
```
https://academia1.yourdomain.com/reset-password/confirm?token_hash=abc123
https://academia2.yourdomain.com/reset-password/confirm?token_hash=def456
```

## Benefícios da Implementação

1. **Segurança**: Isolamento completo entre academias
2. **UX**: URLs específicas reforçam a identidade da academia
3. **Escalabilidade**: Sistema funciona para qualquer número de academias
4. **Manutenibilidade**: Código organizado e bem documentado
5. **Compliance**: Atende requisitos de segurança para dados sensíveis

## Testes Recomendados

1. **Teste de isolamento**: Verificar se token de uma academia não funciona em outra
2. **Teste de expiração**: Confirmar que tokens expirados são rejeitados
3. **Teste de reutilização**: Verificar que tokens usados não funcionam novamente
4. **Teste de validação**: Confirmar que dados inválidos são rejeitados
5. **Teste de UX**: Verificar fluxo completo do ponto de vista do usuário
