# Implementation Plan

- [x] 1. Create database schema and migration files





  - Create SQL migration file for user_status_history table with all required columns and constraints
  - Create optimized indexes for performance on tenant_id, branch_id, user_id, and changed_at columns
  - Add foreign key constraints to ensure referential integrity
  - _Requirements: 1.1, 1.4, 2.1, 5.1, 5.5_

- [x] 2. Implement database trigger system





  - Create PL/pgSQL trigger function track_user_status_changes() that detects status changes
  - Implement logic to extract change reason from user metadata and current user context
  - Create trigger on users table that calls the function after UPDATE operations
  - Add error handling and transaction safety to prevent data loss
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5_

- [ ] 3. Create data migration script for existing users
  - Write SQL script to populate initial status history records for all existing users
  - Implement logic to create baseline records with current status and creation timestamp
  - Add validation to ensure all users have at least one history record after migration
  - Create rollback script in case migration needs to be reverted
  - _Requirements: 8.1, 8.2, 8.3, 8.4, 8.5_

- [x] 4. Implement optimized RPC functions for metrics





  - Create get_users_metrics_with_history() function that calculates active/inactive users using status history
  - Implement get_daily_users_chart_data_with_history() function for chart data with precise daily counts
  - Add proper date handling for Brazil timezone (America/Sao_Paulo)
  - Optimize queries using window functions and CTEs for better performance
  - _Requirements: 3.1, 3.2, 3.3, 3.4, 3.5, 5.1, 5.4_

- [x] 5. Create TypeScript interfaces and types





  - Define UserStatusHistory interface with all required properties and proper typing
  - Extend existing StudentsChartData interface to include suspended users count
  - Create utility types for status change operations and history queries
  - Add JSDoc comments for better developer experience
  - _Requirements: 6.1, 6.2, 6.3, 6.4, 6.5_

- [x] 6. Update metrics actions to use history-based data





  - Modify getStudentsMetrics() function to call new RPC function with history data
  - Update getStudentsChartData() function to use history-based chart data
  - Implement fallback logic for cases where RPC functions are not available
  - Add proper error handling and logging for debugging
  - _Requirements: 7.1, 7.2, 7.5_

- [x] 7. Update dashboard chart components





  - Modify StudentsLineChart component to display suspended users as a third line
  - Update chart configuration to handle the new suspended users data
  - Ensure proper color coding and legend for all three status types
  - Add tooltips showing exact counts for each status on hover
  - _Requirements: 4.1, 4.2, 4.3, 4.5_

- [x] 8. Implement daily active students metrics with history





  - Update getDailyActiveStudents() function to use history-based data
  - Modify getDailyNewStudents() function to use precise historical counts
  - Update getDailyRetentionRate() function to calculate based on actual status changes
  - Ensure all functions respect tenant and branch isolation
  - _Requirements: 7.3, 7.4, 3.4, 3.5_

- [ ] 9. Create database indexes and performance optimizations
  - Implement composite indexes for common query patterns (tenant_id + changed_at)
  - Add partial indexes for active status queries to improve performance
  - Create covering indexes for frequently accessed columns
  - Test query performance with large datasets and optimize as needed
  - _Requirements: 5.1, 5.2, 5.3, 5.5_

- [ ] 10. Add user status change utilities and helpers
  - Create utility functions for safely changing user status with reason tracking
  - Implement helper functions to query user status at specific dates
  - Add functions to get complete status history for individual users
  - Create validation functions to ensure status changes are valid
  - _Requirements: 6.1, 6.2, 6.6, 3.6_

- [ ] 11. Update existing dashboard actions to use new system
  - Modify getDashboardData() function to use history-based metrics
  - Update all chart data functions to use the new historical data sources
  - Ensure backward compatibility during transition period
  - Add feature flags to switch between old and new systems if needed
  - _Requirements: 7.1, 7.2, 7.3, 7.4, 4.4_

- [ ] 12. Implement comprehensive testing suite
  - Write unit tests for all new RPC functions with various scenarios
  - Create integration tests for trigger functionality and data consistency
  - Add performance tests to ensure queries meet the 2-second requirement
  - Implement end-to-end tests for dashboard functionality with historical data
  - _Requirements: 5.1, 2.4, 1.5, 4.5_

- [ ] 13. Create migration deployment scripts
  - Write deployment script that applies schema changes in correct order
  - Implement data migration with progress tracking and error recovery
  - Create verification scripts to validate migration success
  - Add monitoring and alerting for migration process
  - _Requirements: 8.1, 8.2, 8.3, 2.5_

- [ ] 14. Update documentation and add monitoring
  - Document new database schema and trigger system
  - Create API documentation for new RPC functions
  - Add monitoring for trigger performance and error rates
  - Create alerts for data consistency issues or performance degradation
  - _Requirements: 5.2, 5.3, 2.4, 2.5_