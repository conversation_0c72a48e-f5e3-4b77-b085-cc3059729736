/**
 * Arquivo de índice para todos os tipos do sistema
 * 
 * Centraliza as exportações de tipos para facilitar importações
 * e manter a organização do código.
 */

// Tipos compartilhados
export * from './shared'

// Tipos específicos de histórico de status
export * from './user-status-history'

// Tipos de métricas baseadas em histórico
export * from './metrics-history'

// Utilitários de tipos
export * from './utils/status-helpers'

// Tipos de atendimento (se existir)
export * from './attendance'

// Tipos de responsáveis (se existir)
export * from './guardian'