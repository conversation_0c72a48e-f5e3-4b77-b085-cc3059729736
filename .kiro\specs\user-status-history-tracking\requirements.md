# Requirements Document

## Introduction

Este documento define os requisitos para implementar um sistema completo de rastreamento de histórico de status de usuários no ApexSaaS. O sistema deve registrar todas as mudanças de status (ativo/inativo/suspenso) dos usuários com timestamps precisos, mantendo um histórico completo e imutável para análises e métricas. O sistema deve ser isolado por tenant_id e branch_id para suportar a arquitetura multi-tenant da plataforma.

## Requirements

### Requirement 1

**User Story:** Como administrador do sistema, eu quero que todas as mudanças de status de usuários sejam registradas automaticamente, para que eu possa ter um histórico completo e auditável de quando cada usuário foi ativado ou inativado.

#### Acceptance Criteria

1. WHEN um usuário tem seu status alterado de 'active' para 'inactive' THEN o sistema SHALL registrar automaticamente esta mudança com timestamp preciso, tenant_id, branch_id e motivo da alteração
2. WHEN um usuário tem seu status alterado de 'inactive' para 'active' THEN o sistema SHALL registrar automaticamente esta mudança com timestamp preciso, tenant_id, branch_id e motivo da alteração
3. WHEN um usuário tem seu status alterado para 'suspended' THEN o sistema SHALL registrar automaticamente esta mudança com timestamp preciso, tenant_id, branch_id e motivo da alteração
4. WHEN uma mudança de status é registrada THEN o sistema SHALL preservar o registro permanentemente sem possibilidade de exclusão
5. WHEN múltiplas mudanças de status ocorrem para o mesmo usuário THEN o sistema SHALL manter todos os registros em ordem cronológica

### Requirement 2

**User Story:** Como desenvolvedor, eu quero que o sistema de histórico seja implementado através de triggers automáticos no banco de dados, para que não haja dependência de código da aplicação e garanta que nenhuma mudança seja perdida.

#### Acceptance Criteria

1. WHEN a tabela users é atualizada com mudança no campo status THEN um trigger SHALL ser executado automaticamente
2. WHEN o trigger é executado THEN ele SHALL inserir um novo registro na tabela user_status_history
3. WHEN o trigger detecta que o status não mudou THEN ele SHALL não inserir nenhum registro
4. WHEN o trigger falha THEN a transação de atualização do usuário SHALL ser revertida
5. WHEN o sistema é reiniciado THEN os triggers SHALL continuar funcionando automaticamente

### Requirement 3

**User Story:** Como analista de dados, eu quero consultar métricas precisas de usuários ativos/inativos por data específica, para que eu possa gerar relatórios e gráficos históricos exatos.

#### Acceptance Criteria

1. WHEN eu consulto usuários ativos em uma data específica THEN o sistema SHALL retornar o número exato baseado no histórico de status
2. WHEN eu consulto usuários inativos em uma data específica THEN o sistema SHALL retornar o número exato baseado no histórico de status
3. WHEN eu consulto dados para um período de tempo THEN o sistema SHALL retornar dados dia a dia com precisão
4. WHEN eu filtro por tenant_id THEN o sistema SHALL retornar apenas dados daquele tenant
5. WHEN eu filtro por branch_id THEN o sistema SHALL retornar apenas dados daquela filial
6. WHEN eu consulto o histórico de um usuário específico THEN o sistema SHALL retornar todas as mudanças de status em ordem cronológica

### Requirement 4

**User Story:** Como administrador de academia, eu quero visualizar gráficos de linha que mostram a evolução diária de usuários ativos/inativos, para que eu possa acompanhar tendências e tomar decisões baseadas em dados históricos precisos.

#### Acceptance Criteria

1. WHEN eu acesso o dashboard THEN o sistema SHALL exibir gráficos de linha com dados históricos precisos
2. WHEN eu seleciono um período específico THEN o gráfico SHALL mostrar dados dia a dia para aquele período
3. WHEN eu visualizo dados de 30 dias THEN cada ponto do gráfico SHALL representar o número exato de usuários ativos/inativos naquela data
4. WHEN eu filtro por filial THEN o gráfico SHALL mostrar apenas dados daquela branch_id
5. WHEN os dados são carregados THEN eles SHALL ser baseados no histórico real de mudanças de status, não em estimativas

### Requirement 5

**User Story:** Como desenvolvedor, eu quero que o sistema seja otimizado para consultas rápidas de métricas, para que os dashboards carreguem rapidamente mesmo com grandes volumes de dados históricos.

#### Acceptance Criteria

1. WHEN consultas de métricas são executadas THEN elas SHALL retornar resultados em menos de 2 segundos
2. WHEN a tabela de histórico cresce THEN as consultas SHALL manter performance através de índices otimizados
3. WHEN múltiplas consultas simultâneas são feitas THEN o sistema SHALL manter responsividade
4. WHEN dados são agregados por período THEN o sistema SHALL usar funções SQL otimizadas
5. WHEN consultas complexas são executadas THEN elas SHALL usar índices compostos apropriados

### Requirement 6

**User Story:** Como administrador do sistema, eu quero que o histórico de status inclua informações contextuais sobre cada mudança, para que eu possa entender o motivo e contexto de cada alteração.

#### Acceptance Criteria

1. WHEN um status é alterado THEN o sistema SHALL registrar o usuário que fez a alteração (changed_by)
2. WHEN um status é alterado THEN o sistema SHALL permitir registrar um motivo/observação opcional
3. WHEN um status é alterado THEN o sistema SHALL registrar metadados adicionais em formato JSON
4. WHEN um status é alterado THEN o sistema SHALL registrar o timestamp com precisão de milissegundos
5. WHEN um status é alterado THEN o sistema SHALL registrar tanto o status anterior quanto o novo status

### Requirement 7

**User Story:** Como desenvolvedor, eu quero que as funções de métricas existentes sejam atualizadas para usar o novo sistema de histórico, para que os dados exibidos nos dashboards sejam baseados no histórico real e não em estimativas.

#### Acceptance Criteria

1. WHEN as funções getStudentsMetrics são chamadas THEN elas SHALL usar dados do histórico de status
2. WHEN as funções getStudentsChartData são chamadas THEN elas SHALL usar dados do histórico de status
3. WHEN as funções getDailyActiveStudents são chamadas THEN elas SHALL usar dados do histórico de status
4. WHEN as funções de retenção são chamadas THEN elas SHALL usar dados do histórico de status
5. WHEN funções RPC otimizadas são criadas THEN elas SHALL usar o novo sistema de histórico

### Requirement 8

**User Story:** Como administrador do sistema, eu quero que o sistema seja compatível com dados históricos existentes, para que não haja perda de informações durante a migração.

#### Acceptance Criteria

1. WHEN o sistema é implementado THEN ele SHALL criar registros iniciais baseados no status atual dos usuários
2. WHEN dados existentes são migrados THEN eles SHALL manter a integridade referencial
3. WHEN a migração é executada THEN ela SHALL ser reversível em caso de problemas
4. WHEN usuários existentes são processados THEN eles SHALL ter pelo menos um registro inicial no histórico
5. WHEN a migração é concluída THEN todos os usuários SHALL ter dados consistentes no histórico