/**
 * Utilitários e helpers para tipos relacionados ao histórico de status
 * 
 * Este arquivo contém funções auxiliares, type guards e utilitários
 * para trabalhar com os tipos do sistema de histórico de status.
 */

import { 
  UserStatus, 
  UserStatusHistory, 
  StatusChangeValidation,
  VALID_STATUS_TRANSITIONS,
  StatusTransition
} from '../user-status-history'

/**
 * Type guard para verificar se um valor é um status válido
 * 
 * @param value - Valor a ser verificado
 * @returns True se o valor é um UserStatus válido
 */
export function isValidUserStatus(value: any): value is UserStatus {
  return typeof value === 'string' && ['active', 'inactive', 'suspended'].includes(value)
}

/**
 * Type guard para verificar se um objeto é um UserStatusHistory válido
 * 
 * @param obj - Objeto a ser verificado
 * @returns True se o objeto é um UserStatusHistory válido
 */
export function isUserStatusHistory(obj: any): obj is UserStatusHistory {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.id === 'string' &&
    typeof obj.tenant_id === 'string' &&
    typeof obj.branch_id === 'string' &&
    typeof obj.user_id === 'string' &&
    (obj.old_status === null || isValidUserStatus(obj.old_status)) &&
    isValidUserStatus(obj.new_status) &&
    typeof obj.changed_at === 'string' &&
    typeof obj.created_at === 'string'
  )
}

/**
 * Valida se uma mudança de status é permitida
 * 
 * @param fromStatus - Status atual (pode ser null para usuários novos)
 * @param toStatus - Status desejado
 * @returns Resultado da validação
 */
export function validateStatusChange(
  fromStatus: UserStatus | null, 
  toStatus: UserStatus
): StatusChangeValidation {
  const fromKey = fromStatus || 'null'
  const transitions = VALID_STATUS_TRANSITIONS[fromKey]
  
  if (!transitions) {
    return {
      is_valid: false,
      message: `Status de origem inválido: ${fromStatus}`,
      error_code: 'INVALID_FROM_STATUS'
    }
  }
  
  const validTransition = transitions.find(t => t.to === toStatus)
  
  if (!validTransition) {
    return {
      is_valid: false,
      message: `Transição não encontrada de ${fromStatus} para ${toStatus}`,
      error_code: 'TRANSITION_NOT_FOUND'
    }
  }
  
  if (!validTransition.allowed) {
    return {
      is_valid: false,
      message: validTransition.reason || `Transição não permitida de ${fromStatus} para ${toStatus}`,
      error_code: 'TRANSITION_NOT_ALLOWED'
    }
  }
  
  return {
    is_valid: true,
    message: validTransition.reason || `Transição válida de ${fromStatus} para ${toStatus}`,
    metadata: {
      transition: validTransition
    }
  }
}

/**
 * Obtém todas as transições válidas a partir de um status
 * 
 * @param fromStatus - Status de origem
 * @returns Lista de transições válidas
 */
export function getValidTransitions(fromStatus: UserStatus | null): StatusTransition[] {
  const fromKey = fromStatus || 'null'
  const transitions = VALID_STATUS_TRANSITIONS[fromKey]
  
  return transitions ? transitions.filter(t => t.allowed) : []
}

/**
 * Obtém os próximos status possíveis a partir de um status atual
 * 
 * @param fromStatus - Status atual
 * @returns Array de status possíveis
 */
export function getNextPossibleStatuses(fromStatus: UserStatus | null): UserStatus[] {
  const validTransitions = getValidTransitions(fromStatus)
  return validTransitions.map(t => t.to)
}

/**
 * Formata um status para exibição amigável
 * 
 * @param status - Status a ser formatado
 * @returns String formatada para exibição
 */
export function formatStatusForDisplay(status: UserStatus | null): string {
  const statusMap: Record<string, string> = {
    'active': 'Ativo',
    'inactive': 'Inativo',
    'suspended': 'Suspenso'
  }
  
  return status ? statusMap[status] || status : 'Não definido'
}

/**
 * Obtém a cor associada a um status para uso em interfaces
 * 
 * @param status - Status para obter a cor
 * @returns Código de cor hexadecimal
 */
export function getStatusColor(status: UserStatus | null): string {
  const colorMap: Record<string, string> = {
    'active': '#10b981',    // Verde
    'inactive': '#6b7280',  // Cinza
    'suspended': '#f59e0b'  // Amarelo/Laranja
  }
  
  return status ? colorMap[status] || '#6b7280' : '#6b7280'
}

/**
 * Obtém o ícone associado a um status
 * 
 * @param status - Status para obter o ícone
 * @returns Nome do ícone (compatível com Lucide React)
 */
export function getStatusIcon(status: UserStatus | null): string {
  const iconMap: Record<string, string> = {
    'active': 'check-circle',
    'inactive': 'x-circle',
    'suspended': 'pause-circle'
  }
  
  return status ? iconMap[status] || 'help-circle' : 'help-circle'
}

/**
 * Calcula a diferença em dias entre duas datas
 * 
 * @param startDate - Data inicial
 * @param endDate - Data final
 * @returns Número de dias de diferença
 */
export function calculateDaysDifference(startDate: string, endDate: string): number {
  const start = new Date(startDate)
  const end = new Date(endDate)
  const diffTime = Math.abs(end.getTime() - start.getTime())
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
}

/**
 * Formata uma data para o timezone do Brasil
 * 
 * @param date - Data a ser formatada
 * @returns Data formatada no timezone do Brasil
 */
export function formatDateToBrazilTimezone(date: string | Date): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  
  return dateObj.toLocaleDateString('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    timeZone: 'America/Sao_Paulo'
  })
}

/**
 * Formata uma data e hora para o timezone do Brasil
 * 
 * @param date - Data a ser formatada
 * @returns Data e hora formatadas no timezone do Brasil
 */
export function formatDateTimeToBrazilTimezone(date: string | Date): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  
  return dateObj.toLocaleString('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    timeZone: 'America/Sao_Paulo'
  })
}

/**
 * Cria um objeto de metadados padrão para mudanças de status
 * 
 * @param source - Fonte da mudança (ex: 'manual', 'automatic', 'import')
 * @param additionalData - Dados adicionais a serem incluídos
 * @returns Objeto de metadados
 */
export function createDefaultMetadata(
  source: string = 'manual',
  additionalData: Record<string, any> = {}
): Record<string, any> {
  return {
    source,
    timestamp: new Date().toISOString(),
    user_agent: typeof window !== 'undefined' ? window.navigator.userAgent : 'server',
    ...additionalData
  }
}

/**
 * Agrupa registros de histórico por data
 * 
 * @param history - Array de registros de histórico
 * @returns Objeto agrupado por data
 */
export function groupHistoryByDate(
  history: UserStatusHistory[]
): Record<string, UserStatusHistory[]> {
  return history.reduce((groups, record) => {
    const date = formatDateToBrazilTimezone(record.changed_at)
    
    if (!groups[date]) {
      groups[date] = []
    }
    
    groups[date].push(record)
    return groups
  }, {} as Record<string, UserStatusHistory[]>)
}

/**
 * Filtra registros de histórico por status
 * 
 * @param history - Array de registros de histórico
 * @param status - Status para filtrar
 * @returns Array filtrado
 */
export function filterHistoryByStatus(
  history: UserStatusHistory[],
  status: UserStatus
): UserStatusHistory[] {
  return history.filter(record => record.new_status === status)
}

/**
 * Obtém o último registro de histórico de um array
 * 
 * @param history - Array de registros de histórico
 * @returns Último registro ou null se array vazio
 */
export function getLastHistoryRecord(
  history: UserStatusHistory[]
): UserStatusHistory | null {
  if (history.length === 0) return null
  
  return history.reduce((latest, current) => {
    return new Date(current.changed_at) > new Date(latest.changed_at) ? current : latest
  })
}

/**
 * Calcula estatísticas básicas de um array de registros de histórico
 * 
 * @param history - Array de registros de histórico
 * @returns Estatísticas básicas
 */
export function calculateHistoryStats(history: UserStatusHistory[]): {
  total_changes: number
  unique_users: number
  status_distribution: Record<UserStatus, number>
  date_range: { start: string; end: string } | null
} {
  if (history.length === 0) {
    return {
      total_changes: 0,
      unique_users: 0,
      status_distribution: { active: 0, inactive: 0, suspended: 0 },
      date_range: null
    }
  }
  
  const uniqueUsers = new Set(history.map(h => h.user_id)).size
  const statusDistribution = history.reduce((dist, record) => {
    dist[record.new_status] = (dist[record.new_status] || 0) + 1
    return dist
  }, {} as Record<UserStatus, number>)
  
  // Garantir que todos os status estejam presentes
  const completeDistribution: Record<UserStatus, number> = {
    active: statusDistribution.active || 0,
    inactive: statusDistribution.inactive || 0,
    suspended: statusDistribution.suspended || 0
  }
  
  const dates = history.map(h => new Date(h.changed_at)).sort((a, b) => a.getTime() - b.getTime())
  const dateRange = {
    start: dates[0].toISOString(),
    end: dates[dates.length - 1].toISOString()
  }
  
  return {
    total_changes: history.length,
    unique_users: uniqueUsers,
    status_distribution: completeDistribution,
    date_range: dateRange
  }
}