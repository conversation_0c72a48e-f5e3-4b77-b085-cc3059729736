'use client';

/**
 * Hook para geração de senhas aleatórias seguras
 * Utiliza crypto.getRandomValues() para aleatoriedade criptograficamente segura
 */

import { useState, useCallback } from 'react';
import {
    generatePasswordSync,
    type PasswordGeneratorOptions,
    type PasswordGeneratorResult
} from '@/utils/password-generator';

export interface UsePasswordGeneratorReturn {
    generatePassword: (options?: PasswordGeneratorOptions) => PasswordGeneratorResult;
    isGenerating: boolean;
    lastGenerated: PasswordGeneratorResult | null;
}

// Re-exportar tipos para compatibilidade
export type { PasswordGeneratorOptions, PasswordGeneratorResult };

/**
 * Hook para geração de senhas aleatórias seguras
 */
export function usePasswordGenerator(): UsePasswordGeneratorReturn {
    const [isGenerating, setIsGenerating] = useState(false);
    const [lastGenerated, setLastGenerated] = useState<PasswordGeneratorResult | null>(null);

    const generatePassword = useCallback((options: PasswordGeneratorOptions = {}): PasswordGeneratorResult => {
        setIsGenerating(true);

        try {
            const result = generatePasswordSync(options);
            setLastGenerated(result);
            return result;
        } catch (error) {
            console.error('Erro ao gerar senha:', error);
            throw error;
        } finally {
            setIsGenerating(false);
        }
    }, []);

    return {
        generatePassword,
        isGenerating,
        lastGenerated
    };
}

// Re-exportar funções utilitárias para compatibilidade
export { generatePasswordSync, clearSensitiveData, clearMultipleSensitiveData } from '@/utils/password-generator';