"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { alunoFormSchema, type AlunoFormValues } from "../actions/schemas/aluno-schema";
import { createAluno } from "../actions/create-aluno";
import { useStudentsCache } from "./use-students-cache";

export function useAlunosForm(initialData?: Partial<AlunoFormValues>) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [success, setSuccess] = useState(false);
  const router = useRouter();
  const { forceRefresh } = useStudentsCache();
  
  const defaultValues: Partial<AlunoFormValues> = {
    belt: "white",
    status: "active",
    ...initialData
  };
  
  const form = useForm<AlunoFormValues>({
    resolver: zodResolver(alunoFormSchema),
    defaultValues,
  });
  
  const onSubmit = async (values: AlunoFormValues) => {
    setIsSubmitting(true);
    
    try {
      const result = await createAluno(values);
      
      if (result.success) {
        setSuccess(true);
        // Revalidar cache de estudantes quando um novo aluno é criado
        forceRefresh();
        router.refresh();
      } else {
        form.setError("root", { 
          message: result.error || "Ocorreu um erro ao criar o aluno. Tente novamente." 
        });
      }
    } catch (error) {
      console.error("Erro ao criar aluno:", error);
      form.setError("root", { 
        message: "Ocorreu um erro ao criar o aluno. Tente novamente." 
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return {
    form,
    isSubmitting,
    success,
    onSubmit,
    formState: form.formState,
  };
} 