'use client';

import { useFormContext } from "react-hook-form";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Card, CardContent } from "@/components/ui/card";
import { NovoAlunoFormValues } from "../../../actions/schemas/aluno-schema";
import { useEffect, useState } from "react";
import { useTenantTheme } from "@/hooks/tenant/use-tenant-theme";
import { cn } from "@/lib/utils";
import { CreditCard, Clock, Users, CheckCircle2, Loader2 } from "lucide-react";
import { getActivePlansByModality } from "@/app/(dashboard)/academia/actions/plan-actions";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

interface Plan {
  id: string;
  title: string;
  description?: string;
  plan_type: string;
  pricing_config: {
    type: 'one_time' | 'recurring' | 'annual';
    amount: number;
    currency?: string;
    frequency?: string;
    frequency_number?: number;
    late_days?: number;
  };
  duration_config: {
    type: 'ongoing' | 'limited' | 'specific';
    value?: number;
    unit?: 'days' | 'months';
    end_date?: string;
  };
  access_config: {
    max_students?: number;
    features?: string[];
  };
  status: string;
}

export default function PlanSelectionSection() {
  const { control, watch, setValue } = useFormContext<NovoAlunoFormValues>();
  const selectedPlanId = watch('selected_plan_id') || '';
  const selectedModalityId = watch('selected_modality_id') || '';
  const { primaryColor } = useTenantTheme();

  const [plans, setPlans] = useState<Plan[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Limpar seleção de plano quando modalidade mudar
  useEffect(() => {
    if (selectedPlanId) {
      setValue('selected_plan_id', '');
    }
  }, [selectedModalityId, setValue]);

  // Carregar planos ativos filtrados pela modalidade selecionada
  useEffect(() => {
    const loadPlans = async () => {
      try {
        setIsLoading(true);
        setError(null);

        console.log('Carregando planos para modalidade:', selectedModalityId);
        const result = await getActivePlansByModality(selectedModalityId || undefined);
        console.log('Resultado da busca de planos:', result);

        if (result.success && result.data) {
          // Garantir que result.data seja um array válido
          const plansData = Array.isArray(result.data) ? result.data : [];
          console.log('Planos filtrados:', plansData);
          setPlans(plansData);
        } else {
          console.error('Erro na resposta:', result.errors);
          setError(result.errors?._form || 'Erro ao carregar planos disponíveis');
        }
      } catch (err) {
        console.error('Erro ao carregar planos:', err);
        setError('Erro inesperado ao carregar planos');
      } finally {
        setIsLoading(false);
      }
    };

    loadPlans();
  }, [selectedModalityId]); // Recarregar quando a modalidade mudar

  // Função para formatar preço
  const formatPrice = (pricing: Plan['pricing_config']) => {
    const amount = pricing.amount || 0;
    const formatted = new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: pricing.currency || 'BRL'
    }).format(amount); // Valor já está em reais

    if (pricing.type === 'recurring') {
      const frequency = pricing.frequency || 'month';
      const frequencyNumber = pricing.frequency_number || 1;

      // Mapear frequency para unidade em português (consistente com enrollment-modal)
      const periodUnit =
        frequency === 'year' || frequency === 'yearly' || frequency === 'ano'
          ? 'ano'
          : frequency === 'month' || frequency === 'monthly' || frequency === 'mês' || frequency === 'mes'
          ? 'mês'
          : frequency === 'week' || frequency === 'weekly' || frequency === 'semana'
          ? 'semana'
          : frequency === 'day' || frequency === 'daily' || frequency === 'dia'
          ? 'dia'
          : 'período';

      // Função para pluralizar
      const pluralize = (unit: string, qty: number) => {
        if (qty === 1) return unit;
        if (unit === 'mês') return 'meses';
        if (unit === 'ano') return 'anos';
        if (unit === 'semana') return 'semanas';
        if (unit === 'dia') return 'dias';
        return unit;
      };

      const frequencyText = frequencyNumber === 1
        ? periodUnit
        : `${frequencyNumber} ${pluralize(periodUnit, frequencyNumber)}`;

      return `${formatted}/${frequencyText}`;
    } else if (pricing.type === 'annual') {
      return `${formatted}/ano`;
    }

    return formatted;
  };

  // Função para formatar duração
  const formatDuration = (duration: Plan['duration_config']) => {
    if (duration.type === 'ongoing') {
      return 'Contínuo';
    } else if (duration.type === 'limited') {
      const unit = duration.unit === 'months' ? 'meses' : 'dias';
      return `${duration.value} ${unit}`;
    } else if (duration.type === 'specific') {
      return `Até ${new Date(duration.end_date || '').toLocaleDateString('pt-BR')}`;
    }

    return 'Não especificado';
  };

  // Função para obter ícone do tipo de plano
  const getPlanTypeIcon = (planType: string) => {
    switch (planType.toLowerCase()) {
      case 'premium':
        return <CreditCard className="w-4 h-4" />;
      case 'basic':
        return <Users className="w-4 h-4" />;
      default:
        return <CheckCircle2 className="w-4 h-4" />;
    }
  };

  // Função para obter cor do badge do tipo de plano
  const getPlanTypeBadgeColor = (planType: string) => {
    switch (planType.toLowerCase()) {
      case 'premium':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'basic':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (isLoading) {
    return (
      <Card className="overflow-hidden border-slate-200 dark:border-slate-700 shadow-sm bg-white dark:bg-slate-800">
        <div className="bg-slate-50 dark:bg-slate-700/50 px-6 py-4 border-b border-slate-200 dark:border-slate-700">
          <h2 className="text-lg font-semibold text-slate-900 dark:text-slate-100 flex items-center">
            <CreditCard className="w-5 h-5 mr-2 text-slate-500 dark:text-slate-400" />
            Seleção de Plano
          </h2>
        </div>
        <CardContent className="p-6">
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-6 h-6 animate-spin text-slate-400" />
            <span className="ml-2 text-slate-600 dark:text-slate-400">Carregando planos...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="overflow-hidden border-slate-200 dark:border-slate-700 shadow-sm bg-white dark:bg-slate-800">
        <div className="bg-slate-50 dark:bg-slate-700/50 px-6 py-4 border-b border-slate-200 dark:border-slate-700">
          <h2 className="text-lg font-semibold text-slate-900 dark:text-slate-100 flex items-center">
            <CreditCard className="w-5 h-5 mr-2 text-slate-500 dark:text-slate-400" />
            Seleção de Plano
          </h2>
        </div>
        <CardContent className="p-6">
          <div className="text-center py-8">
            <p className="text-red-600 dark:text-red-400">{error}</p>
            <Button
              variant="outline"
              onClick={() => window.location.reload()}
              className="mt-4"
            >
              Tentar novamente
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="overflow-hidden border-slate-200 dark:border-slate-700 shadow-sm hover:shadow transition-all duration-200 bg-white dark:bg-slate-800">
      <div className="bg-slate-50 dark:bg-slate-700/50 px-6 py-4 border-b border-slate-200 dark:border-slate-700">
        <h2 className="text-lg font-semibold text-slate-900 dark:text-slate-100 flex items-center">
          <CreditCard className="w-5 h-5 mr-2 text-slate-500 dark:text-slate-400" />
          Seleção de Plano
        </h2>
        <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
          Escolha o plano para matricular o novo aluno
        </p>
      </div>

      <CardContent className="p-6">
        <FormField
          control={control}
          name="selected_plan_id"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <RadioGroup
                  value={field.value || ''}
                  onValueChange={field.onChange}
                  className="space-y-4"
                >
                  {selectedModalityId && plans.map((plan) => (
                    <div key={plan.id} className="relative">
                      <RadioGroupItem
                        value={plan.id}
                        id={plan.id}
                        className="peer sr-only"
                      />
                      <label
                        htmlFor={plan.id}
                        className={cn(
                          "flex cursor-pointer rounded-lg border-2 border-slate-200 dark:border-slate-700 p-4 hover:border-slate-300 dark:hover:border-slate-600 transition-all",
                          "peer-checked:border-primary peer-checked:bg-primary/5 dark:peer-checked:bg-primary/10",
                          selectedPlanId === plan.id && "border-primary bg-primary/5 dark:bg-primary/10"
                        )}
                        style={
                          selectedPlanId === plan.id && primaryColor
                            ? {
                              borderColor: primaryColor,
                              backgroundColor: `${primaryColor}10`
                            }
                            : undefined
                        }
                      >
                        <div className="flex-1">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-2">
                                {getPlanTypeIcon(plan.plan_type)}
                                <h3 className="font-semibold text-slate-900 dark:text-slate-100">
                                  {plan.title}
                                </h3>
                                <Badge
                                  variant="outline"
                                  className={cn("text-xs", getPlanTypeBadgeColor(plan.plan_type))}
                                >
                                  {plan.plan_type}
                                </Badge>
                              </div>

                              {plan.description && (
                                <p className="text-sm text-slate-600 dark:text-slate-400 mb-3">
                                  {plan.description}
                                </p>
                              )}

                              <div className="flex flex-wrap gap-4 text-sm text-slate-600 dark:text-slate-400">
                                <div className="flex items-center gap-1">
                                  <CreditCard className="w-4 h-4" />
                                  <span>{formatPrice(plan.pricing_config)}</span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <Clock className="w-4 h-4" />
                                  <span>{formatDuration(plan.duration_config)}</span>
                                </div>
                                {plan.access_config.max_students && (
                                  <div className="flex items-center gap-1">
                                    <Users className="w-4 h-4" />
                                    <span>Máx. {plan.access_config.max_students} alunos</span>
                                  </div>
                                )}
                              </div>
                            </div>

                            <div className={cn(
                              "w-5 h-5 rounded-full border-2 flex items-center justify-center ml-4 flex-shrink-0",
                              selectedPlanId === plan.id
                                ? "border-primary bg-primary"
                                : "border-slate-300 dark:border-slate-600"
                            )}
                              style={
                                selectedPlanId === plan.id && primaryColor
                                  ? {
                                    borderColor: primaryColor,
                                    backgroundColor: primaryColor
                                  }
                                  : undefined
                              }
                            >
                              {selectedPlanId === plan.id && (
                                <CheckCircle2 className="w-3 h-3 text-white" />
                              )}
                            </div>
                          </div>
                        </div>
                      </label>
                    </div>
                  ))}
                </RadioGroup>
              </FormControl>
              <FormMessage className="mt-2" />
            </FormItem>
          )}
        />

        {!selectedModalityId && (
          <div className="text-center py-8">
            <p className="text-slate-600 dark:text-slate-400">
              Selecione uma modalidade primeiro para ver os planos disponíveis.
            </p>
          </div>
        )}

        {selectedModalityId && plans.length === 0 && !isLoading && (
          <div className="text-center py-8">
            <p className="text-slate-600 dark:text-slate-400">
              Nenhum plano ativo encontrado para esta modalidade.
            </p>
            <p className="text-sm text-slate-500 dark:text-slate-500 mt-1">
              Configure planos na seção Academia para poder matricular alunos.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
