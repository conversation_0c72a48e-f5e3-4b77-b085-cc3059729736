"use client";

import { useTenantTheme } from "@/hooks/tenant/use-tenant-theme";
import { ReactNode } from 'react';

interface PageTitleProps {
  title?: string | React.ReactNode;
  subtitle?: string;
  icon?: ReactNode;
}

export function PageTitle({ title, subtitle, icon }: PageTitleProps) {
  const { primaryColor } = useTenantTheme();

  if (!title) return null;

  return (
    <div className="flex items-center gap-2 sm:gap-3 min-w-0">
      {icon && (
        <div className="flex h-8 w-8 sm:h-10 sm:w-10 items-center justify-center rounded-lg bg-primary/10 flex-shrink-0">
          {icon}
        </div>
      )}
      <div className="min-w-0 flex-1">
        <h1 className="text-lg sm:text-xl lg:text-2xl font-bold tracking-tight line-clamp-1">
          {title}
        </h1>
        {subtitle && (
          <p className="text-xs sm:text-sm text-muted-foreground line-clamp-1 mt-0.5">
            {subtitle}
          </p>
        )}
      </div>
    </div>
  );
}