'use client';

import { Fragment, useMemo } from "react";
import { Popover, PopoverButton, PopoverPanel, Transition } from "@headlessui/react";
import { BellIcon } from "@heroicons/react/24/outline";
import { CheckCircleIcon, XCircleIcon, InformationCircleIcon, ExclamationTriangleIcon, EllipsisVerticalIcon } from "@heroicons/react/20/solid";
import Link from "next/link";
import { useTenantTheme } from "@/hooks/tenant/use-tenant-theme";
import { useUserMetadata } from "@/hooks/user/Auth/useUserMetadata";
import { useNotificationCount } from "@/hooks/notifications/use-notification-count";
import { useNotifications } from "@/hooks/notifications/use-notifications";
import { useNotificationAnimation } from "@/hooks/notifications/use-notification-animation";
import { formatDistanceToNow } from "date-fns";
import type { NotificationCategory, NotificationStatus } from "@/services/notifications/types/notification-types";

// Função para obter ícone baseado na categoria
function getCategoryIcon(category: NotificationCategory) {
  switch (category) {
    case 'success':
      return (
        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/30">
          <CheckCircleIcon className="h-5 w-5 text-green-600 dark:text-green-400" />
        </div>
      );
    case 'error':
      return (
        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/30">
          <XCircleIcon className="h-5 w-5 text-red-600 dark:text-red-400" />
        </div>
      );
    case 'alert':
      return (
        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-yellow-100 dark:bg-yellow-900/30">
          <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
        </div>
      );
    case 'info':
    case 'reminder':
    default:
      return (
        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/30">
          <InformationCircleIcon className="h-5 w-5 text-blue-600 dark:text-blue-400" />
        </div>
      );
  }
}

// Função para formatar tempo relativo
function formatRelativeTime(dateString: string) {
  try {
    return formatDistanceToNow(new Date(dateString), {
      addSuffix: true
    });
  } catch {
    return 'Agora mesmo';
  }
}

export function NotificationsPopover() {
  const { primaryColor } = useTenantTheme();
  const { metadata } = useUserMetadata();

  // Hooks de notificações - só executam se temos um usuário
  const { count, loading: countLoading } = useNotificationCount({
    userId: metadata?.id || ''
  });

  // Hook para animações de notificação
  const { hasNewNotification, shouldAnimate } = useNotificationAnimation({
    count,
    loading: countLoading
  });

  // Estabilizar o objeto filters para evitar re-criações
  const stableFilters = useMemo(() => ({
    limit: 5,
    status: ['unread' as NotificationStatus]
  }), []);

  const {
    notifications,
    loading: notificationsLoading,
    markAsRead,
    markAllAsRead
  } = useNotifications({
    userId: metadata?.id || '',
    filters: stableFilters,
    realTime: true
  });

  // Se não temos usuário, não renderizar
  if (!metadata?.id) {
    return null;
  }

  const handleMarkAllAsRead = async () => {
    try {
      await markAllAsRead();
    } catch (error) {
      console.error('Erro ao marcar todas como lidas:', error);
    }
  };

  const handleMarkAsRead = async (notificationId: string) => {
    try {
      await markAsRead(notificationId);
    } catch (error) {
      console.error('Erro ao marcar como lida:', error);
    }
  };

  return (
    <Popover className="dropdown-overlay relative outline-none">
      <PopoverButton className="-m-2 p-2 text-gray-400 hover:text-gray-500 focus:outline-none dark:text-gray-500 dark:hover:text-gray-400 outline-none rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
        <span className="sr-only">Ver notificações</span>
        <div className="relative">
          <BellIcon
            className={`h-5 w-5 sm:h-6 sm:w-6 transition-all duration-300 ${
              shouldAnimate
                ? hasNewNotification
                  ? 'notification-bell-active text-orange-500 dark:text-orange-400'
                  : 'notification-bell-active text-gray-600 dark:text-gray-300'
                : 'hover:scale-110'
            }`}
            aria-hidden="true"
          />
          {!countLoading && count > 0 && (
            <>
              {/* Badge principal com contador */}
              <span
                className={`absolute -top-1.5 -right-1.5 flex h-4 w-4 items-center justify-center rounded-full text-[10px] font-medium text-white z-10 ${
                  hasNewNotification ? 'notification-new' : 'notification-badge-pulse'
                }`}
                style={{ backgroundColor: primaryColor || '#ef4444' }}
              >
                {count > 99 ? '99+' : count}
              </span>

              {/* Anel de glow animado - só aparece quando há nova notificação */}
              {hasNewNotification && (
                <span
                  className="absolute -top-1.5 -right-1.5 h-4 w-4 rounded-full notification-badge-glow"
                  style={{ backgroundColor: primaryColor || '#ef4444' }}
                />
              )}
            </>
          )}
        </div>
      </PopoverButton>
      <Transition
        as={Fragment}
        enter="transition ease-out duration-200"
        enterFrom="opacity-0 translate-y-1"
        enterTo="opacity-100 translate-y-0"
        leave="transition ease-in duration-150"
        leaveFrom="opacity-100 translate-y-0"
        leaveTo="opacity-0 translate-y-1"
      >
        <PopoverPanel className="dropdown-overlay fixed sm:absolute right-0 sm:right-0 top-16 sm:top-auto sm:mt-2 w-screen sm:w-96 transform sm:max-w-md">
          <div className="overflow-hidden rounded-none sm:rounded-xl shadow-2xl ring-1 ring-black/5 bg-white dark:bg-gray-900 dark:ring-white/10 mx-0 sm:mx-0">
            {/* Header */}
            <div className="border-b border-gray-100 dark:border-gray-800 px-4 py-3 sm:px-6 sm:py-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Notificações</h3>
                {notifications.length > 0 && (
                  <button
                    onClick={handleMarkAllAsRead}
                    className="text-sm font-medium hover:opacity-80 transition-opacity rounded-md px-3 py-1.5 hover:bg-gray-100 dark:hover:bg-gray-800"
                    style={{ color: primaryColor || '#2563eb' }}
                  >
                    Marcar todas como lidas
                  </button>
                )}
              </div>
            </div>

            {/* Content */}
            <div className="max-h-80 sm:max-h-96 overflow-y-auto">
              {notificationsLoading ? (
                // Loading skeleton
                <div className="space-y-1">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="flex gap-3 p-3 sm:p-4 border-b border-gray-50 dark:border-gray-800/50">
                      <div className="flex-shrink-0">
                        <div className="h-8 w-8 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse" />
                      </div>
                      <div className="flex-1 min-w-0 space-y-2">
                        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
                        <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-3/4" />
                        <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-1/2" />
                      </div>
                    </div>
                  ))}
                </div>
              ) : notifications.length === 0 ? (
                // Estado vazio
                <div className="text-center py-12 px-4">
                  <div className="mx-auto h-16 w-16 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center mb-4">
                    <BellIcon className="h-8 w-8 text-gray-400" />
                  </div>
                  <h3 className="text-base font-medium text-gray-900 dark:text-white mb-2">
                    Nenhuma notificação
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Você está em dia com tudo!
                  </p>
                </div>
              ) : (
                // Lista de notificações
                <div className="divide-y divide-gray-50 dark:divide-gray-800/50">
                  {notifications.map((notification, index) => (
                    <div
                      key={notification.id}
                      className="group relative flex gap-3 p-3 sm:p-4 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors duration-150 cursor-pointer"
                      onClick={() => handleMarkAsRead(notification.id)}
                    >
                      <div className="flex-shrink-0">
                        {getCategoryIcon(notification.category)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 dark:text-white leading-5">
                              {notification.title}
                            </p>
                            <p className="mt-1 text-sm text-gray-600 dark:text-gray-300 leading-5">
                              {notification.message}
                            </p>
                            <p className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                              {formatRelativeTime(notification.created_at)}
                            </p>
                          </div>
                          <button className="opacity-0 group-hover:opacity-100 transition-opacity p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded">
                            <EllipsisVerticalIcon className="h-4 w-4 text-gray-400" />
                          </button>
                        </div>
                        {/* Indicador de não lida */}
                        {notification.status === 'unread' && (
                          <div
                            className="absolute left-1 top-1/2 transform -translate-y-1/2 w-2 h-2 rounded-full"
                            style={{ backgroundColor: primaryColor || '#2563eb' }}
                          />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Footer */}
            {!notificationsLoading && (
              <div className="border-t border-gray-100 dark:border-gray-800 p-3 sm:p-4">
                <Link
                  href="/notificacoes"
                  className="block w-full rounded-lg p-2.5 sm:p-3 text-center text-sm font-medium hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                  style={{ color: primaryColor || '#2563eb' }}
                >
                  Ver todas as notificações
                </Link>
              </div>
            )}
          </div>
        </PopoverPanel>
      </Transition>
    </Popover>
  );
}