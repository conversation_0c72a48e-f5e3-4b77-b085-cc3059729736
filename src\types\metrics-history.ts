/**
 * Tipos específicos para métricas baseadas em histórico de status
 * 
 * Este arquivo contém interfaces e tipos relacionados especificamente
 * às métricas e análises que utilizam o histórico de status de usuários.
 */

import { UserStatus } from './user-status-history'

/**
 * Interface para métricas de usuários com base no histórico
 * 
 * Estende as métricas tradicionais com dados mais precisos
 * baseados no histórico real de mudanças de status.
 */
export interface StudentsMetricsWithHistory {
  /** Usuários ativos no momento atual */
  active_users_now: number
  
  /** Usuários ativos no final do mês anterior */
  active_users_last_month: number
  
  /** Novos usuários ativos no mês atual */
  new_users_now: number
  
  /** Novos usuários ativos no mês anterior */
  new_users_last_month: number
  
  /** Usuários suspensos atualmente (novo campo) */
  suspended_users_now: number
  
  /** Usuários inativos atualmente */
  inactive_users_now: number
  
  /** Data de referência para os cálculos */
  reference_date: string
  
  /** Indica se os dados são baseados em histórico real */
  is_history_based: boolean
}

/**
 * Interface para dados de gráfico diário com histórico
 * 
 * Representa dados precisos para gráficos de linha que mostram
 * a evolução diária dos status de usuários.
 */
export interface DailyUsersChartDataWithHistory {
  /** Data no formato legível (DD/MM) */
  day_date: string
  
  /** Data completa em formato ISO */
  full_date: string
  
  /** Contagem de usuários ativos nesta data */
  active_count: number
  
  /** Contagem de usuários inativos nesta data */
  inactive_count: number
  
  /** Contagem de usuários suspensos nesta data */
  suspended_count: number
  
  /** Total de usuários nesta data */
  total_count: number
  
  /** Mudanças de status que ocorreram nesta data */
  status_changes_count: number
}

/**
 * Interface para parâmetros de consulta de métricas com histórico
 * 
 * Define os parâmetros aceitos pelas funções RPC que calculam
 * métricas baseadas no histórico de status.
 */
export interface HistoryMetricsQueryParams {
  /** ID do tenant (obrigatório) */
  p_tenant_id: string
  
  /** ID da filial (opcional) */
  p_branch_id?: string | null
  
  /** Número de dias para análise (padrão: 30) */
  p_days?: number
  
  /** Número de meses para análise (padrão: 6) */
  p_months?: number
  
  /** Data de referência (opcional, padrão: hoje) */
  p_reference_date?: string
  
  /** Incluir usuários suspensos nos cálculos */
  p_include_suspended?: boolean
}

/**
 * Interface para resposta das funções RPC de métricas
 * 
 * Estrutura padrão retornada pelas funções RPC que calculam
 * métricas baseadas no histórico de status.
 */
export interface HistoryMetricsRPCResponse {
  /** Dados das métricas */
  data: any[]
  
  /** Erro, se houver */
  error: any
  
  /** Status da consulta */
  status: number
  
  /** Mensagem de status */
  statusText: string
}

/**
 * Interface para métricas de retenção baseadas em histórico
 * 
 * Calcula taxas de retenção mais precisas usando o histórico
 * real de mudanças de status dos usuários.
 */
export interface RetentionMetricsWithHistory {
  /** Taxa de retenção atual */
  current_retention_rate: number
  
  /** Taxa de retenção do período anterior */
  previous_retention_rate: number
  
  /** Mudança percentual na retenção */
  retention_change_percentage: number
  
  /** Tendência da retenção */
  retention_trend: 'improving' | 'declining' | 'stable'
  
  /** Número de usuários retidos */
  retained_users_count: number
  
  /** Número total de usuários no cálculo */
  total_users_count: number
  
  /** Período de referência */
  reference_period: {
    start_date: string
    end_date: string
  }
}

/**
 * Interface para análise de churn baseada em histórico
 * 
 * Fornece insights sobre a taxa de abandono (churn) dos usuários
 * baseada no histórico real de mudanças de status.
 */
export interface ChurnAnalysisWithHistory {
  /** Taxa de churn atual */
  current_churn_rate: number
  
  /** Taxa de churn do período anterior */
  previous_churn_rate: number
  
  /** Mudança percentual no churn */
  churn_change_percentage: number
  
  /** Tendência do churn */
  churn_trend: 'increasing' | 'decreasing' | 'stable'
  
  /** Usuários que fizeram churn no período */
  churned_users_count: number
  
  /** Principais motivos de churn */
  churn_reasons: Array<{
    reason: string
    count: number
    percentage: number
  }>
  
  /** Análise por segmento */
  churn_by_segment: Array<{
    segment: string
    churn_rate: number
    users_count: number
  }>
}

/**
 * Interface para métricas de crescimento baseadas em histórico
 * 
 * Analisa o crescimento da base de usuários usando dados
 * históricos precisos de ativação e desativação.
 */
export interface GrowthMetricsWithHistory {
  /** Taxa de crescimento atual */
  current_growth_rate: number
  
  /** Taxa de crescimento do período anterior */
  previous_growth_rate: number
  
  /** Mudança na taxa de crescimento */
  growth_rate_change: number
  
  /** Novos usuários no período */
  new_users_count: number
  
  /** Usuários reativados no período */
  reactivated_users_count: number
  
  /** Usuários perdidos no período */
  lost_users_count: number
  
  /** Crescimento líquido */
  net_growth: number
  
  /** Projeção de crescimento */
  growth_projection: {
    next_month: number
    next_quarter: number
    confidence_level: number
  }
}

/**
 * Interface para dashboard de métricas com histórico
 * 
 * Agrega todas as métricas baseadas em histórico em uma
 * estrutura única para uso em dashboards.
 */
export interface HistoryBasedDashboardMetrics {
  /** Métricas básicas de usuários */
  user_metrics: StudentsMetricsWithHistory
  
  /** Dados para gráficos diários */
  daily_chart_data: DailyUsersChartDataWithHistory[]
  
  /** Métricas de retenção */
  retention_metrics: RetentionMetricsWithHistory
  
  /** Análise de churn */
  churn_analysis: ChurnAnalysisWithHistory
  
  /** Métricas de crescimento */
  growth_metrics: GrowthMetricsWithHistory
  
  /** Timestamp da última atualização */
  last_updated: string
  
  /** Período coberto pelos dados */
  data_period: {
    start_date: string
    end_date: string
  }
  
  /** Qualidade dos dados */
  data_quality: {
    completeness_percentage: number
    has_gaps: boolean
    gap_periods?: Array<{
      start_date: string
      end_date: string
      reason: string
    }>
  }
}

/**
 * Tipo para configuração de cálculo de métricas
 * 
 * Permite personalizar como as métricas são calculadas
 * baseadas no histórico de status.
 */
export type MetricsCalculationConfig = {
  /** Incluir finais de semana nos cálculos */
  include_weekends?: boolean
  
  /** Timezone para cálculos de data */
  timezone?: string
  
  /** Considerar apenas mudanças durante horário comercial */
  business_hours_only?: boolean
  
  /** Filtros de status a serem considerados */
  status_filters?: UserStatus[]
  
  /** Peso para diferentes tipos de mudança */
  change_weights?: Record<string, number>
  
  /** Configurações de agregação */
  aggregation_settings?: {
    method: 'sum' | 'average' | 'median'
    group_by: 'day' | 'week' | 'month'
  }
}