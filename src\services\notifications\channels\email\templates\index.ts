/**
 * Exportações centralizadas dos templates de e-mail
 * Facilita a importação e uso dos templates em outros módulos
 */

// Template base e componentes auxiliares
export {
  BaseEmailTemplate,
  EmailButton,
  EmailText,
  EmailHeading,
  EmailDivider,
  type BaseEmailTemplateProps,
  type EmailButtonProps,
  type EmailTextProps,
  type EmailHeadingProps
} from './base-email-template';

// Template de lembrete de aula
export {
  ClassReminderTemplate,
  type ClassReminderTemplateProps
} from './class-reminder-template';

// Template de lembrete de pagamento de mensalidade
export {
  PaymentReminderTemplate,
  type PaymentReminderTemplateProps
} from './payment-reminder-template';

// Template de pagamento que vence em breve
export {
  PaymentDueSoonTemplate,
  type PaymentDueSoonTemplateProps
} from './payment-due-soon-template';

// Template de pagamento que vence hoje
export {
  PaymentDueTodayTemplate,
  type PaymentDueTodayTemplateProps
} from './payment-due-today-template';

// Template de pagamento em atraso
export {
  PaymentOverdueTemplate,
  type PaymentOverdueTemplateProps
} from './payment-overdue-template';

// Template de boas-vindas
export {
  WelcomeTemplate,
  type WelcomeTemplateProps
} from './welcome-template';

// Template de convite para eventos
export {
  EventInvitationTemplate,
  type EventInvitationTemplateProps
} from './event-invitation-template';

// Template de alertas administrativos
export {
  AdminAlertTemplate,
  type AdminAlertTemplateProps
} from './admin-alert-template';

// Template de vencimento de pagamento de matrícula
export {
  EnrollmentPaymentDueTemplate,
  type EnrollmentPaymentDueTemplateProps
} from './enrollment-payment-due-template';

// Template de vencimento de despesas (para dono da academia)
export {
  ExpenseDueTemplate,
  type ExpenseDueTemplateProps
} from './expense-due-template';

// Template de notificação de nova matrícula (para dono da academia)
export {
  NewEnrollmentNotificationTemplate,
  type NewEnrollmentNotificationTemplateProps
} from './new-enrollment-notification-template';

// Template padrão do sistema
export {
  SystemDefaultTemplate,
  type SystemDefaultTemplateProps
} from './system-default-template';

// Template de confirmação de pagamento para administradores
export {
  PaymentConfirmationAdminTemplate,
  type PaymentConfirmationAdminTemplateProps
} from './payment-confirmation-admin-template';

// Template de redefinição de senha
export {
  PasswordResetTemplate,
  type PasswordResetTemplateProps
} from './password-reset-template';

// Template de credenciais de acesso
export {
  CredentialsTemplate,
  type CredentialsTemplateProps
} from './credentials-template';

// Tipos de templates disponíveis
export type EmailTemplateType =
  | 'class-reminder'
  | 'payment-reminder'
  | 'payment-due-soon'
  | 'payment-due-today'
  | 'payment-overdue'
  | 'welcome'
  | 'event-invitation'
  | 'admin-alert'
  | 'enrollment-payment-due'
  | 'expense-due'
  | 'new-enrollment-notification'
  | 'payment-confirmation-admin'
  | 'password-reset'
  | 'credentials'
  | 'system-default';

// Props unificadas para todos os templates (usando any para evitar problemas de importação circular)
export type EmailTemplateProps = any;

// Metadados dos templates
export interface EmailTemplateMetadata {
  id: EmailTemplateType;
  name: string;
  description: string;
  category: 'student' | 'owner' | 'system';
  requiredProps: string[];
}

export const EMAIL_TEMPLATE_METADATA: Record<EmailTemplateType, EmailTemplateMetadata> = {
  'class-reminder': {
    id: 'class-reminder',
    name: 'Lembrete de Aula',
    description: 'Notifica alunos sobre aulas próximas',
    category: 'student',
    requiredProps: ['academyName', 'studentName', 'className', 'classDate', 'classTime']
  },
  'payment-reminder': {
    id: 'payment-reminder',
    name: 'Lembrete de Pagamento',
    description: 'Lembra alunos sobre mensalidades vencendo ou em atraso',
    category: 'student',
    requiredProps: ['academyName', 'studentName', 'amount', 'dueDate', 'planName']
  },
  'payment-due-soon': {
    id: 'payment-due-soon',
    name: 'Pagamento Vence em Breve',
    description: 'Lembrete amigável para pagamentos que vencem em 3 dias',
    category: 'student',
    requiredProps: ['academyName', 'studentName', 'amount', 'dueDate', 'planName']
  },
  'payment-due-today': {
    id: 'payment-due-today',
    name: 'Pagamento Vence Hoje',
    description: 'Alerta para pagamentos que vencem no dia atual',
    category: 'student',
    requiredProps: ['academyName', 'studentName', 'amount', 'dueDate', 'planName']
  },
  'payment-overdue': {
    id: 'payment-overdue',
    name: 'Pagamento em Atraso',
    description: 'Notificação para pagamentos em atraso com diferentes níveis de urgência',
    category: 'student',
    requiredProps: ['academyName', 'studentName', 'amount', 'dueDate', 'planName', 'overdueDays', 'overdueLevel']
  },
  'welcome': {
    id: 'welcome',
    name: 'Boas-vindas',
    description: 'Mensagem de boas-vindas para novos alunos',
    category: 'student',
    requiredProps: ['academyName', 'studentName', 'planName', 'startDate', 'enrollmentDate']
  },
  'event-invitation': {
    id: 'event-invitation',
    name: 'Convite para Evento',
    description: 'Convida alunos para eventos especiais da academia',
    category: 'student',
    requiredProps: ['academyName', 'eventName', 'eventDescription', 'eventDate', 'eventLocation']
  },
  'admin-alert': {
    id: 'admin-alert',
    name: 'Alerta Administrativo',
    description: 'Alertas para gestores sobre situações que requerem atenção',
    category: 'owner',
    requiredProps: ['academyName', 'adminName', 'alertType', 'alertTitle', 'alertMessage']
  },
  'enrollment-payment-due': {
    id: 'enrollment-payment-due',
    name: 'Vencimento de Matrícula',
    description: 'Notifica sobre vencimento de pagamento de matrícula',
    category: 'student',
    requiredProps: ['academyName', 'studentName', 'amount', 'dueDate', 'enrollmentDate', 'planName']
  },
  'expense-due': {
    id: 'expense-due',
    name: 'Vencimento de Despesas',
    description: 'Notifica o dono sobre despesas da academia vencendo',
    category: 'owner',
    requiredProps: ['academyName', 'ownerName', 'expenseDescription', 'amount', 'dueDate', 'category']
  },
  'new-enrollment-notification': {
    id: 'new-enrollment-notification',
    name: 'Nova Matrícula',
    description: 'Notifica o dono sobre novas matrículas realizadas',
    category: 'owner',
    requiredProps: ['academyName', 'ownerName', 'studentName', 'studentEmail', 'planName', 'enrollmentDate', 'startDate', 'amount', 'paymentStatus']
  },
  'payment-confirmation-admin': {
    id: 'payment-confirmation-admin',
    name: 'Confirmação de Pagamento - Admin',
    description: 'Notifica administradores sobre confirmação de pagamento de aluno',
    category: 'owner',
    requiredProps: ['academyName', 'adminName', 'paymentId', 'amount', 'studentName', 'studentEmail', 'studentUserId', 'planName', 'confirmedAt']
  },
  'password-reset': {
    id: 'password-reset',
    name: 'Redefinição de Senha',
    description: 'Template para redefinição de senha de usuários',
    category: 'system',
    requiredProps: ['academyName', 'userName', 'resetLink', 'expirationTime']
  },
  'credentials': {
    id: 'credentials',
    name: 'Credenciais de Acesso',
    description: 'Template para envio de credenciais de acesso para novos alunos',
    category: 'student',
    requiredProps: ['academyName', 'studentName', 'studentEmail', 'temporaryPassword', 'loginUrl']
  },
  'system-default': {
    id: 'system-default',
    name: 'Template Padrão',
    description: 'Template genérico para notificações do sistema',
    category: 'system',
    requiredProps: ['academyName', 'title', 'message']
  }
};

// Função auxiliar para obter metadados por tipo
export function getEmailTemplateMetadata(type: EmailTemplateType) {
  return EMAIL_TEMPLATE_METADATA[type];
}

// Função para validar se as props necessárias estão presentes
export function validateTemplateProps(type: EmailTemplateType, props: any): boolean {
  const metadata = getEmailTemplateMetadata(type);
  return metadata.requiredProps.every(prop => props[prop] !== undefined);
}

// Exportar utilitários de mapeamento
export {
  mapTemplateDataToProps,
  getEmailTemplateType,
  convertToReactEmailTemplate,
  NOTIFICATION_TO_EMAIL_TEMPLATE_MAP,
  type TemplateData
} from './template-mapper';
