# Design Document

## Overview

Este documento descreve o design técnico para implementar geração aleatória de senhas e notificação por e-mail para novos alunos. A solução será composta por um hook React para geração de senhas, um template de e-mail específico para credenciais, e integração com o sistema de notificações existente.

## Architecture

### Componentes Principais

1. **usePasswordGenerator Hook** - Hook React para geração de senhas seguras
2. **CredentialsEmailTemplate** - Template React Email para envio de credenciais
3. **NotificationDispatcher Integration** - Integração com o dispatcher existente
4. **CreateAluno Action Enhancement** - Modificação da action de criação de aluno

### Fluxo de Dados

```mermaid
sequenceDiagram
    participant Admin as Administrador
    participant Form as Formulário
    participant Action as create-aluno.ts
    participant Hook as usePasswordGenerator
    participant Auth as Supabase Auth
    participant Dispatcher as NotificationDispatcher
    participant Email as EmailChannel
    participant Template as CredentialsTemplate
    
    Admin->>Form: Preenche dados do aluno
    Form->>Action: Submete formulário
    Action->>Hook: Gera senha aleatória
    Hook-->>Action: Retorna senha segura
    Action->>Auth: Cria usuário com senha gerada
    Auth-->>Action: Usuário criado
    Action->>Dispatcher: Envia credenciais por e-mail
    Dispatcher->>Email: Processa notificação
    Email->>Template: Renderiza template
    Template-->>Email: HTML renderizado
    Email-->>Dispatcher: E-mail enviado
    Dispatcher-->>Action: Status do envio
    Action-->>Form: Resultado da criação
```

## Components and Interfaces

### 1. usePasswordGenerator Hook

```typescript
interface PasswordGeneratorOptions {
  length?: number;
  includeUppercase?: boolean;
  includeLowercase?: boolean;
  includeNumbers?: boolean;
  includeSpecialChars?: boolean;
  excludeSimilar?: boolean;
}

interface PasswordGeneratorResult {
  password: string;
  strength: 'weak' | 'medium' | 'strong';
  entropy: number;
}

interface UsePasswordGeneratorReturn {
  generatePassword: (options?: PasswordGeneratorOptions) => PasswordGeneratorResult;
  isGenerating: boolean;
  lastGenerated: PasswordGeneratorResult | null;
}
```

### 2. Credentials Email Template

```typescript
interface CredentialsEmailTemplateProps {
  // Dados da academia
  academyName: string;
  academyLogo?: string;
  primaryColor?: string;
  secondaryColor?: string;
  tenantSlug?: string;
  
  // Dados do aluno
  studentName: string;
  studentEmail: string;
  temporaryPassword: string;
  
  // URLs e configurações
  loginUrl: string;
  supportEmail?: string;
  supportPhone?: string;
  dashboardUrl?: string;
  
  // Instruções personalizadas
  welcomeMessage?: string;
  customInstructions?: string[];
}
```

### 3. Notification Dispatcher Integration

```typescript
interface SendCredentialsData {
  tenantId: string;
  userId: string;
  studentName: string;
  studentEmail: string;
  temporaryPassword: string;
  loginUrl: string;
  channels?: ['email']; // Apenas e-mail
}
```

## Data Models

### Password Generation Configuration

```typescript
const DEFAULT_PASSWORD_CONFIG: PasswordGeneratorOptions = {
  length: 12,
  includeUppercase: true,
  includeLowercase: true,
  includeNumbers: true,
  includeSpecialChars: true,
  excludeSimilar: true // Exclui caracteres similares como 0, O, l, I
};

const CHARACTER_SETS = {
  uppercase: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
  lowercase: 'abcdefghijklmnopqrstuvwxyz',
  numbers: '0123456789',
  specialChars: '!@#$%^&*()_+-=[]{}|;:,.<>?',
  similarChars: '0O1lI' // Caracteres a serem excluídos se excludeSimilar = true
};
```

### Template Variables Mapping

```typescript
const CREDENTIALS_TEMPLATE_VARIABLES = {
  academyName: 'string',
  academyLogo: 'string?',
  primaryColor: 'string?',
  secondaryColor: 'string?',
  tenantSlug: 'string?',
  studentName: 'string',
  studentEmail: 'string',
  temporaryPassword: 'string',
  loginUrl: 'string',
  supportEmail: 'string?',
  supportPhone: 'string?',
  dashboardUrl: 'string?',
  welcomeMessage: 'string?',
  customInstructions: 'string[]?'
};
```

## Error Handling

### Password Generation Errors

1. **Configuração Inválida**: Validar parâmetros de entrada
2. **Falha na Geração**: Retry automático até 3 tentativas
3. **Critérios Não Atendidos**: Regenerar até atender critérios de segurança

### Email Notification Errors

1. **Template Render Error**: Log error, usar template fallback
2. **Email Send Failure**: Log error, não falhar criação do aluno
3. **User Email Not Found**: Log warning, pular envio de e-mail
4. **Dispatcher Error**: Log error, continuar fluxo normal

### Integration Errors

1. **Hook Initialization Error**: Usar geração de senha padrão
2. **Supabase Auth Error**: Falhar criação do aluno
3. **Database Error**: Falhar criação do aluno

## Testing Strategy

### Unit Tests

1. **usePasswordGenerator Hook**
   - Teste de geração com diferentes configurações
   - Teste de validação de critérios de segurança
   - Teste de performance com múltiplas gerações
   - Teste de exclusão de caracteres similares

2. **CredentialsEmailTemplate**
   - Teste de renderização com dados mínimos
   - Teste de renderização com todos os dados
   - Teste de responsividade em diferentes clientes
   - Teste de escape de caracteres especiais

3. **Integration Functions**
   - Teste de integração com NotificationDispatcher
   - Teste de mapeamento de variáveis do template
   - Teste de tratamento de erros

### Integration Tests

1. **End-to-End Flow**
   - Criar aluno → Gerar senha → Enviar e-mail
   - Teste com falha no envio de e-mail
   - Teste com configurações diferentes de tenant
   - Teste de performance com múltiplos alunos

2. **Email Delivery Tests**
   - Teste de entrega em diferentes provedores
   - Teste de renderização em diferentes clientes
   - Teste de links e URLs no e-mail

### Security Tests

1. **Password Security**
   - Teste de entropia da senha gerada
   - Teste de unicidade entre gerações
   - Teste de critérios de complexidade
   - Teste de resistência a ataques de força bruta

2. **Email Security**
   - Teste de escape de dados sensíveis
   - Teste de validação de e-mail de destino
   - Teste de prevenção de vazamento de informações

## Performance Considerations

### Password Generation

- Usar crypto.getRandomValues() para aleatoriedade segura
- Cache de character sets para evitar recriação
- Validação eficiente de critérios de segurança
- Timeout para evitar loops infinitos

### Email Template

- Template pré-compilado para melhor performance
- Lazy loading de assets (logos, imagens)
- Otimização de CSS inline para compatibilidade
- Compressão de HTML gerado

### Integration Performance

- Envio de e-mail assíncrono (não bloquear criação)
- Retry com backoff exponencial para falhas
- Timeout configurável para operações de rede
- Logging estruturado para debugging

## Security Considerations

### Password Security

- Usar algoritmos criptograficamente seguros
- Nunca logar senhas em plain text
- Limpar variáveis de senha da memória
- Validar força da senha antes de usar

### Email Security

- Validar e-mail de destino antes do envio
- Usar HTTPS para todos os links no e-mail
- Não incluir informações sensíveis além da senha
- Implementar rate limiting para envios

### Data Protection

- Criptografar senha antes de armazenar
- Usar conexões seguras para Supabase
- Implementar audit trail para criação de usuários
- Seguir LGPD para dados pessoais