/**
 * Template React Email simplificado para Edge Functions
 * Versão standalone do template de reset de senha
 */

import React from 'npm:react@18.3.1'
import {
  Html,
  Head,
  Body,
  Container,
  Section,
  Img,
  Heading,
  Text,
  Button,
  Hr,
  Font
} from 'npm:@react-email/components@0.0.22'

export interface PasswordResetTemplateProps {
  academyName: string
  academyLogo?: string
  primaryColor?: string
  secondaryColor?: string
  userEmail: string
  userName?: string
  resetUrl: string
  tokenHash: string
  siteUrl: string
  expirationTime?: string
  supportEmail?: string
  supportPhone?: string
}

export function PasswordResetTemplate({
  academyName,
  academyLogo,
  primaryColor = '#007291',
  secondaryColor = '#004E89',
  userEmail,
  userName,
  resetUrl,
  tokenHash,
  siteUrl,
  expirationTime = '1 hora',
  supportEmail,
  supportPhone
}: PasswordResetTemplateProps) {
  return (
    <Html lang="pt-BR">
      <Head>
        <Font
          fontFamily="Inter"
          fallbackFontFamily="Arial"
          webFont={{
            url: 'https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hiJ-Ek-_EeA.woff2',
            format: 'woff2',
          }}
          fontWeight={400}
          fontStyle="normal"
        />
        <meta name="description" content={`Redefinir senha - ${academyName}`} />
      </Head>
      <Body style={bodyStyle}>
        <Container style={containerStyle}>
          {/* Header */}
          <Section style={headerStyle}>
            {academyLogo && (
              <Img
                src={academyLogo}
                alt={`Logo ${academyName}`}
                style={logoStyle}
              />
            )}
            <Heading style={{ ...academyNameStyle, color: primaryColor }}>
              {academyName}
            </Heading>
          </Section>

          {/* Content */}
          <Section style={contentStyle}>
            <Heading style={{ ...titleStyle, color: primaryColor }}>
              Redefinir Senha
            </Heading>
            
            <Text style={textStyle}>
              {userName ? `Olá, ${userName}!` : 'Olá!'}
            </Text>
            
            <Text style={textStyle}>
              Recebemos uma solicitação para redefinir a senha da sua conta em <strong>{academyName}</strong> 
              associada ao e-mail <strong>{userEmail}</strong>.
            </Text>
            
            <Text style={textStyle}>
              Se você fez esta solicitação, clique no botão abaixo para criar uma nova senha:
            </Text>
            
            <Section style={buttonContainerStyle}>
              <Button href={resetUrl} style={{ ...buttonStyle, backgroundColor: primaryColor }}>
                Redefinir Minha Senha
              </Button>
            </Section>
            
            <Hr style={dividerStyle} />
            
            <Text style={smallTextStyle}>
              <strong>Informações importantes:</strong>
            </Text>
            
            <Text style={smallTextStyle}>
              • Este link é válido por {expirationTime}<br/>
              • O link só pode ser usado uma vez<br/>
              • Se você não solicitou esta redefinição, pode ignorar este e-mail com segurança
            </Text>
            
            <Hr style={dividerStyle} />
            
            <Text style={smallTextStyle}>
              Se o botão não funcionar, copie e cole este link no seu navegador:
            </Text>
            
            <Text style={{ ...smallTextStyle, color: '#3b82f6', textAlign: 'center', wordBreak: 'break-all' }}>
              <a href={resetUrl} style={{ color: '#3b82f6' }}>
                {resetUrl}
              </a>
            </Text>
            
            <Hr style={dividerStyle} />
            
            <Text style={{ ...smallTextStyle, color: '#ef4444', textAlign: 'center' }}>
              <strong>⚠️ Nunca compartilhe este link com outras pessoas</strong>
            </Text>
            
            <Text style={{ ...smallTextStyle, textAlign: 'center' }}>
              Por motivos de segurança, se você não solicitou esta redefinição de senha, 
              recomendamos que entre em contato conosco imediatamente.
            </Text>
          </Section>

          {/* Footer */}
          <Section style={{ ...footerStyle, borderTop: `2px solid ${primaryColor}` }}>
            <Text style={footerTextStyle}>
              {academyName}
            </Text>
            <Text style={footerTextStyle}>
              Este e-mail foi enviado automaticamente. Por favor, não responda.
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  )
}

// Estilos
const bodyStyle = {
  backgroundColor: '#f6f9fc',
  fontFamily: 'Inter, Arial, sans-serif',
  margin: 0,
  padding: 0
}

const containerStyle = {
  maxWidth: '600px',
  margin: '0 auto',
  backgroundColor: '#ffffff',
  borderRadius: '8px',
  overflow: 'hidden',
  boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
}

const headerStyle = {
  textAlign: 'center' as const,
  padding: '32px 24px 24px',
  backgroundColor: '#ffffff'
}

const logoStyle = {
  display: 'block',
  margin: '0 auto 16px',
  maxHeight: '80px',
  maxWidth: '200px',
  width: 'auto',
  height: 'auto'
}

const academyNameStyle = {
  fontSize: '24px',
  fontWeight: '600',
  margin: '0',
  lineHeight: '1.2'
}

const contentStyle = {
  padding: '0 24px 32px'
}

const titleStyle = {
  fontSize: '32px',
  fontWeight: '700',
  textAlign: 'center' as const,
  margin: '24px 0 16px',
  lineHeight: '1.2'
}

const textStyle = {
  fontSize: '16px',
  lineHeight: '1.6',
  color: '#374151',
  margin: '16px 0'
}

const smallTextStyle = {
  fontSize: '14px',
  lineHeight: '1.5',
  color: '#6b7280',
  margin: '16px 0'
}

const buttonContainerStyle = {
  textAlign: 'center' as const,
  margin: '32px 0'
}

const buttonStyle = {
  color: '#ffffff',
  padding: '12px 24px',
  borderRadius: '6px',
  textDecoration: 'none',
  fontWeight: '500',
  fontSize: '16px',
  border: 'none',
  cursor: 'pointer',
  display: 'inline-block'
}

const dividerStyle = {
  borderColor: '#e5e7eb',
  margin: '24px 0'
}

const footerStyle = {
  padding: '24px',
  textAlign: 'center' as const,
  backgroundColor: '#f8fafc'
}

const footerTextStyle = {
  fontSize: '12px',
  color: '#64748b',
  margin: '4px 0',
  lineHeight: '1.4'
}

export default PasswordResetTemplate
