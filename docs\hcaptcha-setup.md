# Configuração do hCaptcha

Este documento explica como configurar o hCaptcha no sistema de reset de senha.

## 1. Criando uma conta no hCaptcha

1. Acesse [https://dashboard.hcaptcha.com/](https://dashboard.hcaptcha.com/)
2. Crie uma conta ou faça login
3. Vá para "Sites" no menu lateral
4. Clique em "Add new site"

## 2. Configurando o site

1. **Site Name**: Digite um nome para identificar seu site (ex: "ApexSaaS - Produção")
2. **Hostnames**: Adicione os domínios onde o hCaptcha será usado:
   - Para desenvolvimento: `localhost`, `127.0.0.1`
   - Para produção: seu domínio real (ex: `app.apexsaas.com`)
3. **Difficulty**: Escolha a dificuldade do captcha
   - **Easy**: Mais fácil para usuários, menos seguro
   - **Moderate**: Balanceado (recomendado)
   - **Difficult**: <PERSON><PERSON>, mais seguro
4. Clique em "Save"

## 3. <PERSON><PERSON><PERSON><PERSON> as chaves

Após criar o site, você receberá:

- **Site Key** (pública): Usada no frontend
- **Secret Key** (privada): Usada no backend para validação

## 4. Configurando as variáveis de ambiente

Adicione as seguintes variáveis ao seu arquivo `.env`:

```env
# hCaptcha configuration
NEXT_PUBLIC_HCAPTCHA_SITE_KEY=sua-site-key-aqui
HCAPTCHA_SECRET_KEY=sua-secret-key-aqui
```

### Exemplo com chaves de teste

Para desenvolvimento, você pode usar as chaves de teste do hCaptcha:

```env
# hCaptcha test keys (sempre passam na validação)
NEXT_PUBLIC_HCAPTCHA_SITE_KEY=10000000-ffff-ffff-ffff-000000000001
HCAPTCHA_SECRET_KEY=0x0000000000000000000000000000000000000000
```

## 5. Configuração no Supabase (Opcional)

O Supabase também suporta hCaptcha nativamente. Para configurar:

1. Acesse o painel do Supabase
2. Vá para "Authentication" > "Settings"
3. Na seção "Security", encontre "CAPTCHA"
4. Ative o hCaptcha e adicione suas chaves

Ou configure via `supabase/config.toml`:

```toml
[auth.captcha]
enabled = true
provider = "hcaptcha"
secret = "sua-secret-key-aqui"
```

## 6. Testando a implementação

### Desenvolvimento
1. Use as chaves de teste fornecidas acima
2. O hCaptcha sempre será validado com sucesso
3. Teste o fluxo completo de reset de senha

### Produção
1. Use suas chaves reais do hCaptcha
2. Teste em diferentes dispositivos e navegadores
3. Monitore os logs para verificar se há erros de validação

## 7. Monitoramento

### Logs do servidor
O sistema registra logs detalhados sobre a validação do hCaptcha:

```
✅ hCaptcha validado com sucesso
❌ hCaptcha validation failed: [códigos de erro]
❌ HCAPTCHA_SECRET_KEY não configurada
```

### Dashboard do hCaptcha
No dashboard do hCaptcha você pode:
- Ver estatísticas de uso
- Monitorar tentativas de validação
- Configurar alertas
- Analisar padrões de tráfego

## 8. Códigos de erro comuns

| Código | Descrição | Solução |
|--------|-----------|---------|
| `missing-input-secret` | Secret key não fornecida | Verificar variável `HCAPTCHA_SECRET_KEY` |
| `invalid-input-secret` | Secret key inválida | Verificar se a chave está correta |
| `missing-input-response` | Token não fornecido | Verificar se o frontend está enviando o token |
| `invalid-input-response` | Token inválido | Token pode ter expirado ou ser inválido |
| `timeout-or-duplicate` | Token expirado ou já usado | Usuário precisa resolver o captcha novamente |

## 9. Personalização

### Tema
O componente hCaptcha suporta temas:
- `light` (padrão)
- `dark`

### Tamanho
- `normal` (padrão)
- `compact`

### Exemplo de personalização no código:

```tsx
<HCaptcha
  sitekey={sitekey}
  onVerify={handleVerify}
  theme="dark"
  size="compact"
/>
```

## 10. Segurança

### Boas práticas:
1. **Nunca exponha a secret key** no frontend
2. **Sempre valide no servidor** - nunca confie apenas na validação do frontend
3. **Use HTTPS** em produção
4. **Monitore tentativas** de bypass
5. **Configure rate limiting** para prevenir ataques

### Rate limiting recomendado:
- Máximo 5 tentativas de reset de senha por IP por hora
- Máximo 3 tentativas de reset por email por dia

## 11. Troubleshooting

### Problema: hCaptcha não carrega
- Verificar se o domínio está configurado corretamente
- Verificar se não há bloqueadores de anúncio interferindo
- Verificar console do navegador para erros JavaScript

### Problema: Validação sempre falha
- Verificar se a secret key está correta
- Verificar se o servidor consegue acessar `https://hcaptcha.com/siteverify`
- Verificar logs do servidor para detalhes do erro

### Problema: Token expirado
- Tokens do hCaptcha expiram em 2 minutos
- Implementar renovação automática se necessário
- Orientar usuários a completar o formulário rapidamente

## 12. Recursos adicionais

- [Documentação oficial do hCaptcha](https://docs.hcaptcha.com/)
- [React hCaptcha no GitHub](https://github.com/hCaptcha/react-hcaptcha)
- [Dashboard do hCaptcha](https://dashboard.hcaptcha.com/)
