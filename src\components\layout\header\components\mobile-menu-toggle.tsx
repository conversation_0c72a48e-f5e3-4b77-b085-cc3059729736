"use client";

import { Bars3Icon } from "@heroicons/react/24/outline";

interface MobileMenuToggleProps {
  setSidebarOpen: (open: boolean) => void;
}

export const MobileMenuToggle = ({ setSidebarOpen }: MobileMenuToggleProps) => {
  return (
    <button
      type="button"
      className="lg:hidden -m-1.5 p-2.5 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100 transition-colors rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50"
      onClick={() => setSidebarOpen(true)}
    >
      <span className="sr-only">Abrir menu</span>
      <Bars3Icon className="h-5 w-5 sm:h-6 sm:w-6" aria-hidden="true" />
    </button>
  );
};