# Template de E-mail para Credenciais

Template React Email para envio de credenciais de acesso para novos alunos da academia.

## Características

- ✅ Design responsivo e profissional
- ✅ Personalização com branding da academia
- ✅ Exibição segura de credenciais
- ✅ Instruções claras de primeiro acesso
- ✅ Suporte a mensagens personalizadas
- ✅ Compatibilidade com múltiplos clientes de e-mail
- ✅ Avisos de segurança integrados

## Uso Básico

```typescript
import { CredentialsTemplate } from '@/services/notifications/channels/email/templates';
import { render } from '@react-email/render';

const html = await render(CredentialsTemplate({
  academyName: 'Academia Guerreiros',
  studentName: '<PERSON>',
  studentEmail: '<EMAIL>',
  temporaryPassword: 'TempPass123!',
  loginUrl: 'https://academia.com/login'
}));
```

## Props Interface

```typescript
interface CredentialsTemplateProps {
  // Dados da academia (obrigatórios)
  academyName: string;
  
  // Dados do aluno (obrigatórios)
  studentName: string;
  studentEmail: string;
  temporaryPassword: string;
  loginUrl: string;
  
  // Personalização visual (opcionais)
  academyLogo?: string;
  primaryColor?: string;      // Padrão: '#007291'
  secondaryColor?: string;    // Padrão: '#004E89'
  tenantSlug?: string;
  
  // Informações de suporte (opcionais)
  supportEmail?: string;
  supportPhone?: string;
  dashboardUrl?: string;
  
  // Conteúdo personalizado (opcionais)
  welcomeMessage?: string;
  customInstructions?: string[];
}
```

## Exemplo Completo

```typescript
const credentialsEmail = await render(CredentialsTemplate({
  // Dados obrigatórios
  academyName: 'Academia Guerreiros',
  studentName: 'Maria Santos',
  studentEmail: '<EMAIL>',
  temporaryPassword: 'SecurePass456!',
  loginUrl: 'https://guerreiros.academia.com/login',
  
  // Personalização visual
  academyLogo: 'https://cdn.academia.com/logo.png',
  primaryColor: '#1e40af',
  secondaryColor: '#1e3a8a',
  tenantSlug: 'guerreiros',
  
  // Informações de suporte
  supportEmail: '<EMAIL>',
  supportPhone: '(11) 99999-9999',
  dashboardUrl: 'https://guerreiros.academia.com/dashboard',
  
  // Conteúdo personalizado
  welcomeMessage: 'Bem-vinda à nossa família de guerreiros! Estamos animados para acompanhar sua evolução.',
  customInstructions: [
    'Traga seu kimono na primeira aula',
    'Chegue 15 minutos antes do horário',
    'Complete seu perfil no sistema'
  ]
}));
```

## Integração com NotificationDispatcher

```typescript
import { NotificationDispatcher } from '@/services/notifications/channels/notification-dispatcher';

const dispatcher = new NotificationDispatcher();

await dispatcher.sendCredentials({
  tenantId: 'tenant-123',
  userId: 'user-456',
  studentName: 'Carlos Oliveira',
  studentEmail: '<EMAIL>',
  temporaryPassword: 'TempPass789!',
  loginUrl: 'https://academia.com/login',
  welcomeMessage: 'Bem-vindo à nossa academia!'
});
```

## Seções do Template

### 1. Cabeçalho
- Logo da academia (se fornecido)
- Nome da academia
- Título "Suas Credenciais de Acesso"

### 2. Saudação
- Mensagem personalizada de boas-vindas
- Nome do aluno destacado

### 3. Credenciais
- E-mail de acesso (destacado)
- Senha temporária (destacada com aviso de segurança)
- Aviso sobre necessidade de alteração

### 4. Instruções de Acesso
- Passo a passo para primeiro login
- Link direto para página de login
- Instruções para alteração de senha

### 5. Recursos Disponíveis
- Lista de funcionalidades do sistema
- Link para dashboard (se fornecido)

### 6. Suporte
- Informações de contato
- E-mail e telefone de suporte (se fornecidos)

### 7. Segurança
- Dicas de segurança
- Avisos sobre confidencialidade

## Personalização Visual

### Cores

```typescript
// Cores padrão
const defaultColors = {
  primaryColor: '#007291',    // Azul principal
  secondaryColor: '#004E89'   // Azul secundário
};

// Personalização
const customTemplate = CredentialsTemplate({
  // ... outras props
  primaryColor: '#dc2626',    // Vermelho
  secondaryColor: '#991b1b'   // Vermelho escuro
});
```

### Logo

```typescript
const templateWithLogo = CredentialsTemplate({
  // ... outras props
  academyLogo: 'https://cdn.academia.com/logo.png'
});
```

### Mensagem Personalizada

```typescript
const templateWithMessage = CredentialsTemplate({
  // ... outras props
  welcomeMessage: 'Seja bem-vindo à nossa família! Estamos ansiosos para vê-lo evoluir conosco.'
});
```

## Instruções Personalizadas

```typescript
const templateWithInstructions = CredentialsTemplate({
  // ... outras props
  customInstructions: [
    'Traga uma toalha e garrafa de água',
    'Use roupas confortáveis para treinar',
    'Chegue 10 minutos antes da aula',
    'Complete seu perfil no primeiro acesso'
  ]
});
```

## Responsividade

O template é otimizado para diferentes dispositivos:

- **Desktop**: Layout completo com todas as seções
- **Mobile**: Layout adaptado com elementos empilhados
- **Email Clients**: Compatível com Gmail, Outlook, Apple Mail, etc.

## Segurança

### Boas Práticas Implementadas

1. **Exibição Segura de Credenciais**:
   - Senha destacada visualmente
   - Avisos sobre temporariedade
   - Instruções para alteração

2. **Links Seguros**:
   - Todos os links usam HTTPS
   - URLs validadas automaticamente

3. **Informações Confidenciais**:
   - Aviso sobre confidencialidade
   - Instruções sobre compartilhamento

### Exemplo de Uso Seguro

```typescript
// ✅ Correto - dados validados
const safeCredentials = {
  studentEmail: validateEmail(email),
  temporaryPassword: sanitizePassword(password),
  loginUrl: validateUrl(url)
};

const template = CredentialsTemplate({
  ...safeCredentials,
  // ... outras props
});

// Limpar dados sensíveis após uso
clearSensitiveData(safeCredentials.temporaryPassword);
```

## Testes

### Teste de Renderização

```typescript
import { render } from '@react-email/render';
import { CredentialsTemplate } from './credentials-template';

describe('CredentialsTemplate', () => {
  it('should render with minimal props', async () => {
    const html = await render(CredentialsTemplate({
      academyName: 'Test Academy',
      studentName: 'Test Student',
      studentEmail: '<EMAIL>',
      temporaryPassword: 'TestPass123!',
      loginUrl: 'https://test.com/login'
    }));
    
    expect(html).toContain('Test Academy');
    expect(html).toContain('Test Student');
    expect(html).toContain('<EMAIL>');
    expect(html).toContain('TestPass123!');
  });
});
```

### Teste de Personalização

```typescript
it('should apply custom colors', async () => {
  const html = await render(CredentialsTemplate({
    // ... props obrigatórias
    primaryColor: '#ff0000',
    secondaryColor: '#cc0000'
  }));
  
  expect(html).toContain('#ff0000');
  expect(html).toContain('#cc0000');
});
```

## Troubleshooting

### Problema: Template não renderiza

**Solução**: Verifique se todas as props obrigatórias estão presentes:

```typescript
// ❌ Problemático - props faltando
const template = CredentialsTemplate({
  academyName: 'Academia'
  // studentName, studentEmail, temporaryPassword, loginUrl faltando
});

// ✅ Correto - todas as props obrigatórias
const template = CredentialsTemplate({
  academyName: 'Academia',
  studentName: 'João',
  studentEmail: '<EMAIL>',
  temporaryPassword: 'Pass123!',
  loginUrl: 'https://academia.com/login'
});
```

### Problema: Cores não aplicadas

**Solução**: Use códigos de cor válidos:

```typescript
// ❌ Problemático
primaryColor: 'azul'

// ✅ Correto
primaryColor: '#007291'  // Hex
primaryColor: 'rgb(0, 114, 145)'  // RGB
primaryColor: 'blue'  // Nome da cor
```

### Problema: Links não funcionam

**Solução**: Use URLs completas com protocolo:

```typescript
// ❌ Problemático
loginUrl: 'academia.com/login'

// ✅ Correto
loginUrl: 'https://academia.com/login'
```

### Problema: Imagens não carregam

**Solução**: Use URLs públicas e acessíveis:

```typescript
// ❌ Problemático
academyLogo: '/logo.png'  // Caminho relativo

// ✅ Correto
academyLogo: 'https://cdn.academia.com/logo.png'  // URL completa
```

## Compatibilidade

### Clientes de E-mail Testados

- ✅ Gmail (Web, Mobile)
- ✅ Outlook (2016+, Web, Mobile)
- ✅ Apple Mail (macOS, iOS)
- ✅ Yahoo Mail
- ✅ Thunderbird
- ✅ Protonmail

### Recursos Suportados

- ✅ CSS Inline
- ✅ Imagens responsivas
- ✅ Links clicáveis
- ✅ Fontes web (com fallback)
- ✅ Cores personalizadas
- ❌ JavaScript (não suportado em e-mails)
- ❌ CSS externo (convertido para inline)

## Melhores Práticas

1. **Sempre teste em múltiplos clientes**
2. **Use URLs absolutas para imagens e links**
3. **Mantenha o HTML simples e semântico**
4. **Inclua texto alternativo para imagens**
5. **Valide dados antes de renderizar**
6. **Implemente fallbacks para recursos não suportados**

## Exemplo de Integração Completa

```typescript
// services/user/credential-service.ts
import { NotificationDispatcher } from '@/services/notifications/channels/notification-dispatcher';
import { generatePasswordSync } from '@/utils/password-generator';

export async function sendUserCredentials(userData: {
  tenantId: string;
  userId: string;
  name: string;
  email: string;
  academyName: string;
}) {
  // Gerar senha segura
  const passwordResult = generatePasswordSync();
  
  // Enviar credenciais
  const dispatcher = new NotificationDispatcher();
  
  const result = await dispatcher.sendCredentials({
    tenantId: userData.tenantId,
    userId: userData.userId,
    studentName: userData.name,
    studentEmail: userData.email,
    temporaryPassword: passwordResult.password,
    loginUrl: `https://${process.env.NEXT_PUBLIC_BASE_DOMAIN}/login`,
    welcomeMessage: `Bem-vindo(a) à ${userData.academyName}!`
  });
  
  // Limpar dados sensíveis
  clearSensitiveData(passwordResult.password);
  
  return result;
}
```