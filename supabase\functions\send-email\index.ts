/**
 * Edge Function para customizar emails de autenticação do Supabase
 * Usa React Email para gerar templates personalizados por academia
 */ import React from 'npm:react@18.3.1';
import { Webhook } from 'https://esm.sh/standardwebhooks@1.0.0';
import { Resend } from 'npm:resend@4.0.0';
import { renderAsync } from 'npm:@react-email/components@0.0.22';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
// Importar o template React Email local
import { PasswordResetTemplate } from './_templates/password-reset-template.tsx';
const resend = new Resend(Deno.env.get('RESEND_API_KEY'));
const hookSecret = Deno.env.get('SEND_EMAIL_HOOK_SECRET');
Deno.serve(async (req)=>{
  if (req.method !== 'POST') {
    return new Response('not allowed', {
      status: 400
    });
  }
  const payload = await req.text();
  const headers = Object.fromEntries(req.headers);
  const wh = new Webhook(hookSecret);
  try {
    const { user, email_data: { token, token_hash, redirect_to, email_action_type } } = wh.verify(payload, headers);
    // Só processar emails de recovery (reset de senha)
    if (email_action_type !== 'recovery') {
      return new Response(JSON.stringify({
        message: 'Email type not handled'
      }), {
        headers: {
          'Content-Type': 'application/json'
        },
        status: 200
      });
    }
    // Criar cliente Supabase
    const supabaseUrl = Deno.env.get('SUPABASE_URL');
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    // Buscar dados do usuário e academia
    const { data: userData, error: userError } = await supabase.from('users').select(`
        id,
        email,
        first_name,
        last_name,
        tenant_id,
        branch_id,
        tenants (
          id,
          name,
          slug,
          logo_url,
          primary_color,
          secondary_color,
          owner_id
        )
      `).eq('id', user.id).single();
    if (userError || !userData) {
      console.error('Erro ao buscar dados do usuário:', userError);
      return new Response(JSON.stringify({
        error: 'User not found'
      }), {
        headers: {
          'Content-Type': 'application/json'
        },
        status: 404
      });
    }
    // Buscar dados do proprietário da academia
    let ownerData = null;
    if (userData.tenants?.owner_id) {
      const { data: owner, error: ownerError } = await supabase.from('users').select('email, phone, first_name, last_name').eq('id', userData.tenants.owner_id).single();
      if (!ownerError && owner) {
        ownerData = owner;
      }
    }
    // Configurar dados da academia
    const academy = {
      id: userData.tenants?.id || 'default',
      name: userData.tenants?.name || 'Academia',
      slug: userData.tenants?.slug || 'default',
      logo: userData.tenants?.logo_url,
      primary_color: userData.tenants?.primary_color || '#007291',
      secondary_color: userData.tenants?.secondary_color || '#004E89',
      support_email: ownerData?.email,
      support_phone: ownerData?.phone
    };

    // Construir URL de reset baseado no slug da academia
    const baseDomain = Deno.env.get('NEXT_PUBLIC_BASE_DOMAIN') || 'localhost:3000';
    const isLocalhost = baseDomain.includes('localhost') || baseDomain.includes('127.0.0.1');

    let resetUrl: string;
    if (isLocalhost) {
      // Desenvolvimento local: http://slug.localhost:3000/reset-password-confirm?token_hash=...
      resetUrl = `http://${academy.slug}.${baseDomain}/reset-password-confirm?token_hash=${token_hash}`;
    } else {
      // Produção: https://slug.domain.com/reset-password-confirm?token_hash=...
      resetUrl = `https://${academy.slug}.${baseDomain}/reset-password-confirm?token_hash=${token_hash}`;
    }
    // Gerar HTML usando React Email
    const emailHtml = await renderAsync(React.createElement(PasswordResetTemplate, {
      academyName: academy.name,
      academyLogo: academy.logo,
      primaryColor: academy.primary_color,
      secondaryColor: academy.secondary_color,
      userEmail: user.email,
      userName: userData.first_name ? `${userData.first_name}${userData.last_name ? ` ${userData.last_name}` : ''}` : undefined,
      resetUrl: resetUrl,
      tokenHash: token_hash,
      siteUrl: Deno.env.get('SUPABASE_URL') ?? '',
      expirationTime: '1 hora',
      supportEmail: academy.support_email,
      supportPhone: academy.support_phone
    }));
    // Enviar email usando Resend
    const { error } = await resend.emails.send({
      from: `${academy.name}@sondtheanime.site`,
      to: [
        user.email
      ],
      subject: `Redefinir senha - ${academy.name}`,
      html: emailHtml
    });
    if (error) {
      throw error;
    }
    console.log('Email de reset enviado com sucesso para:', user.email);
  } catch (error) {
    console.log(error);
    return new Response(JSON.stringify({
      error: {
        http_code: error.code,
        message: error.message
      }
    }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
  const responseHeaders = new Headers();
  responseHeaders.set('Content-Type', 'application/json');
  return new Response(JSON.stringify({}), {
    status: 200,
    headers: responseHeaders
  });
});
