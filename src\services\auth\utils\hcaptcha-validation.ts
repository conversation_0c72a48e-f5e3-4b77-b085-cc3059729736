/**
 * Utilitário para validação do hCaptcha no servidor
 */

interface HCaptchaVerifyResponse {
  success: boolean
  challenge_ts?: string
  hostname?: string
  credit?: boolean
  'error-codes'?: string[]
  score?: number
  score_reason?: string[]
}

/**
 * Valida o token do hCaptcha no servidor
 * @param token Token retornado pelo hCaptcha no frontend
 * @param remoteip IP do cliente (opcional)
 * @returns Promise<boolean> true se válido, false caso contrário
 */
export async function verifyHCaptchaToken(
  token: string, 
  remoteip?: string
): Promise<{ success: boolean; error?: string }> {
  const secretKey = process.env.HCAPTCHA_SECRET_KEY

  if (!secretKey) {
    console.error('HCAPTCHA_SECRET_KEY não configurada')
    return { 
      success: false, 
      error: 'Configuração do hCaptcha não encontrada' 
    }
  }

  if (!token) {
    return { 
      success: false, 
      error: 'Token hCaptcha é obrigatório' 
    }
  }

  try {
    const formData = new URLSearchParams()
    formData.append('secret', secretKey)
    formData.append('response', token)
    
    if (remoteip) {
      formData.append('remoteip', remoteip)
    }

    const response = await fetch('https://hcaptcha.com/siteverify', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: formData,
    })

    if (!response.ok) {
      console.error('Erro na requisição para hCaptcha:', response.status, response.statusText)
      return { 
        success: false, 
        error: 'Erro ao verificar hCaptcha' 
      }
    }

    const result: HCaptchaVerifyResponse = await response.json()

    if (!result.success) {
      console.error('hCaptcha validation failed:', result['error-codes'])
      
      // Mapear códigos de erro para mensagens mais amigáveis
      const errorCodes = result['error-codes'] || []
      let errorMessage = 'Verificação hCaptcha falhou'
      
      if (errorCodes.includes('timeout-or-duplicate')) {
        errorMessage = 'hCaptcha expirou ou já foi usado. Tente novamente.'
      } else if (errorCodes.includes('invalid-input-response')) {
        errorMessage = 'Token hCaptcha inválido'
      } else if (errorCodes.includes('missing-input-response')) {
        errorMessage = 'Token hCaptcha não fornecido'
      }
      
      return { 
        success: false, 
        error: errorMessage 
      }
    }

    return { success: true }

  } catch (error) {
    console.error('Erro ao validar hCaptcha:', error)
    return { 
      success: false, 
      error: 'Erro interno na validação do hCaptcha' 
    }
  }
}

/**
 * Middleware para validar hCaptcha em FormData
 * @param formData FormData contendo o token hCaptcha
 * @param remoteip IP do cliente (opcional)
 * @returns Promise<{ success: boolean; error?: string }>
 */
export async function validateHCaptchaFromFormData(
  formData: FormData, 
  remoteip?: string
): Promise<{ success: boolean; error?: string }> {
  const hcaptchaToken = formData.get('hcaptchaToken')?.toString()
  
  if (!hcaptchaToken) {
    return { 
      success: false, 
      error: 'Token hCaptcha é obrigatório' 
    }
  }

  return verifyHCaptchaToken(hcaptchaToken, remoteip)
}
