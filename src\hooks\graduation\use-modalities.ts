'use client';

import { useQuery } from '@tanstack/react-query';
import { CACHE_KEYS } from '@/constants/cache-keys';
import { getModalitiesForGraduation } from '@/app/(dashboard)/perfil/actions/graduation-actions';
import { useTenant } from '@/hooks/tenant/use-tenant';

interface Modality {
    id: string;
    slug: string;
    name: string;
    enabled: boolean;
}

/**
 * Hook para buscar modalidades ativas para graduação
 * Usa React Query para cache e evita recarregamentos desnecessários
 */
export function useModalities() {
    const { slug: tenantSlug } = useTenant();

    return useQuery<Modality[]>({
        queryKey: CACHE_KEYS.MODALITIES_LIST(tenantSlug),
        queryFn: async () => {
            const result = await getModalitiesForGraduation();

            if (!result.success || !result.data) {
                throw new Error(result.errors?._form || 'Erro ao carregar modalidades');
            }

            return result.data;
        },
        enabled: !!tenantSlug,
        staleTime: 1000 * 60 * 10, // 10 minutos - modalidades não mudam frequentemente
        gcTime: 1000 * 60 * 30, // 30 minutos no cache
        retry: 2,
        refetchOnWindowFocus: false,
    });
}