-- Migration: Create user_status_history table and related infrastructure
-- Date: 2025-01-29
-- Description: Creates the user_status_history table to track all user status changes with automatic triggers

-- Create the user_status_history table
CREATE TABLE user_status_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    branch_id UUID NOT NULL REFERENCES branches(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    old_status TEXT,
    new_status TEXT NOT NULL CHECK (new_status IN ('active', 'inactive', 'suspended')),
    changed_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    changed_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    reason TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Add constraints to ensure data integrity
ALTER TABLE user_status_history 
ADD CONSTRAINT check_old_status_valid 
CHECK (old_status IS NULL OR old_status IN ('active', 'inactive', 'suspended'));

-- Add comment to the table
COMMENT ON TABLE user_status_history IS 'Tracks all user status changes for audit and metrics purposes';

-- Add comments to important columns
COMMENT ON COLUMN user_status_history.old_status IS 'Previous status before the change. NULL for initial records.';
COMMENT ON COLUMN user_status_history.new_status IS 'New status after the change';
COMMENT ON COLUMN user_status_history.changed_at IS 'Timestamp when the status change occurred';
COMMENT ON COLUMN user_status_history.changed_by IS 'User who initiated the status change';
COMMENT ON COLUMN user_status_history.reason IS 'Optional reason for the status change';
COMMENT ON COLUMN user_status_history.metadata IS 'Additional metadata in JSON format';

-- Create optimized indexes for performance
-- Primary index for tenant-based queries with date ordering
CREATE INDEX idx_user_status_history_tenant_date 
ON user_status_history (tenant_id, changed_at DESC);

-- Index for branch-based queries with date ordering
CREATE INDEX idx_user_status_history_branch_date 
ON user_status_history (branch_id, changed_at DESC);

-- Index for user-specific history queries
CREATE INDEX idx_user_status_history_user_date 
ON user_status_history (user_id, changed_at DESC);

-- Composite index for metrics queries by tenant, status and date
CREATE INDEX idx_user_status_history_metrics 
ON user_status_history (tenant_id, new_status, changed_at DESC);

-- Composite index for branch metrics queries
CREATE INDEX idx_user_status_history_branch_metrics 
ON user_status_history (tenant_id, branch_id, new_status, changed_at DESC);

-- Index for queries filtering by changed_by user
CREATE INDEX idx_user_status_history_changed_by 
ON user_status_history (changed_by, changed_at DESC) 
WHERE changed_by IS NOT NULL;

-- Partial index for active status queries (most common)
CREATE INDEX idx_user_status_history_active_status 
ON user_status_history (tenant_id, changed_at DESC) 
WHERE new_status = 'active';

-- Partial index for inactive status queries
CREATE INDEX idx_user_status_history_inactive_status 
ON user_status_history (tenant_id, changed_at DESC) 
WHERE new_status = 'inactive';

-- Partial index for suspended status queries
CREATE INDEX idx_user_status_history_suspended_status 
ON user_status_history (tenant_id, changed_at DESC) 
WHERE new_status = 'suspended';

-- Create trigger function to automatically track status changes
CREATE OR REPLACE FUNCTION track_user_status_changes()
RETURNS TRIGGER AS $$
BEGIN
    -- Only insert if the status actually changed
    IF OLD.status IS DISTINCT FROM NEW.status THEN
        INSERT INTO user_status_history (
            tenant_id,
            branch_id,
            user_id,
            old_status,
            new_status,
            changed_at,
            changed_by,
            reason,
            metadata
        ) VALUES (
            NEW.tenant_id,
            NEW.branch_id,
            NEW.id,
            OLD.status,
            NEW.status,
            NOW(),
            -- Try to get current user from session, fallback to the user being updated
            COALESCE(
                (SELECT auth.uid()),
                NEW.id
            ),
            -- Extract reason from user metadata if available
            COALESCE(
                (NEW.metadata->>'status_change_reason')::TEXT,
                NULL
            ),
            -- Store additional context in metadata
            jsonb_build_object(
                'previous_updated_at', OLD.updated_at,
                'new_updated_at', NEW.updated_at,
                'change_source', 'trigger',
                'old_metadata', OLD.metadata,
                'new_metadata', NEW.metadata
            )
        );
    END IF;
    
    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        -- Log the error but don't fail the main transaction
        RAISE WARNING 'Failed to insert user status history: %', SQLERRM;
        RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create the trigger on users table
CREATE TRIGGER trigger_user_status_history
    AFTER UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION track_user_status_changes();

-- Add RLS policy for user_status_history table
ALTER TABLE user_status_history ENABLE ROW LEVEL SECURITY;

-- Policy for tenant isolation - users can only see records from their tenant
CREATE POLICY "Users can view status history from their tenant" 
ON user_status_history FOR SELECT 
USING (tenant_id = tenant_auth.tenant_id());

-- Policy for inserting records - only the trigger should insert
CREATE POLICY "Only system can insert status history" 
ON user_status_history FOR INSERT 
WITH CHECK (false); -- This will be bypassed by the SECURITY DEFINER trigger function

-- Policy for updates - no updates allowed (immutable audit log)
CREATE POLICY "No updates allowed on status history" 
ON user_status_history FOR UPDATE 
USING (false);

-- Policy for deletes - no deletes allowed (immutable audit log)
CREATE POLICY "No deletes allowed on status history" 
ON user_status_history FOR DELETE 
USING (false);

-- Grant necessary permissions
GRANT SELECT ON user_status_history TO authenticated;
GRANT USAGE ON SCHEMA public TO authenticated;

-- Create a helper function to get user status at a specific date
CREATE OR REPLACE FUNCTION get_user_status_at_date(
    p_user_id UUID,
    p_date DATE,
    p_tenant_id UUID DEFAULT NULL
)
RETURNS TEXT AS $$
DECLARE
    status_result TEXT;
BEGIN
    -- Get the most recent status change before or on the specified date
    SELECT ush.new_status INTO status_result
    FROM user_status_history ush
    WHERE ush.user_id = p_user_id
      AND (p_tenant_id IS NULL OR ush.tenant_id = p_tenant_id)
      AND ush.changed_at::DATE <= p_date
    ORDER BY ush.changed_at DESC
    LIMIT 1;
    
    -- If no history found, get current status from users table
    IF status_result IS NULL THEN
        SELECT u.status INTO status_result
        FROM users u
        WHERE u.id = p_user_id
          AND (p_tenant_id IS NULL OR u.tenant_id = p_tenant_id)
          AND u.created_at::DATE <= p_date;
    END IF;
    
    RETURN COALESCE(status_result, 'inactive');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a helper function to get complete status history for a user
CREATE OR REPLACE FUNCTION get_user_status_history(
    p_user_id UUID,
    p_tenant_id UUID DEFAULT NULL,
    p_limit INTEGER DEFAULT 50
)
RETURNS TABLE (
    id UUID,
    old_status TEXT,
    new_status TEXT,
    changed_at TIMESTAMPTZ,
    changed_by UUID,
    reason TEXT,
    metadata JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ush.id,
        ush.old_status,
        ush.new_status,
        ush.changed_at,
        ush.changed_by,
        ush.reason,
        ush.metadata
    FROM user_status_history ush
    WHERE ush.user_id = p_user_id
      AND (p_tenant_id IS NULL OR ush.tenant_id = p_tenant_id)
    ORDER BY ush.changed_at DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add indexes for the helper functions
CREATE INDEX idx_user_status_history_user_tenant_date 
ON user_status_history (user_id, tenant_id, changed_at DESC);

-- Create a function to validate status transitions (optional business logic)
CREATE OR REPLACE FUNCTION validate_status_transition(
    p_old_status TEXT,
    p_new_status TEXT
)
RETURNS BOOLEAN AS $$
BEGIN
    -- Allow any transition for now, but this can be customized
    -- Example business rules:
    -- - Can't go directly from suspended to active (must go through inactive first)
    -- - Can't change from inactive to suspended directly
    
    -- For now, allow all valid status values
    IF p_new_status NOT IN ('active', 'inactive', 'suspended') THEN
        RETURN FALSE;
    END IF;
    
    -- All transitions are valid for now
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Add a check constraint using the validation function (optional)
-- ALTER TABLE users ADD CONSTRAINT check_valid_status_transition 
-- CHECK (validate_status_transition(status, status));

-- Create an index on the users table status column for better performance
CREATE INDEX IF NOT EXISTS idx_users_status_tenant 
ON users (tenant_id, status, updated_at DESC);

-- Create an index for users by branch and status
CREATE INDEX IF NOT EXISTS idx_users_branch_status 
ON users (branch_id, status, updated_at DESC);