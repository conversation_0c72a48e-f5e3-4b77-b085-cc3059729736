/**
 * Template de e-mail para envio de credenciais de acesso
 * Enviado para novos alunos com suas credenciais de login
 */

import * as React from 'react';
import {
  BaseEmailTemplate,
  EmailHeading,
  EmailText,
  EmailButton,
  EmailDivider
} from './base-email-template';

export interface CredentialsTemplateProps {
  // Dados da academia
  academyName: string;
  academyLogo?: string;
  primaryColor?: string;
  secondaryColor?: string;
  tenantSlug?: string;
  
  // Dados do aluno
  studentName: string;
  studentEmail: string;
  temporaryPassword: string;
  
  // URLs e configurações
  loginUrl: string;
  supportEmail?: string;
  supportPhone?: string;
  dashboardUrl?: string;
  
  // Instruções personalizadas
  welcomeMessage?: string;
  customInstructions?: string[];
}

export function CredentialsTemplate({
  academyName,
  academyLogo,
  primaryColor = '#007291',
  secondaryColor = '#004E89',
  tenantSlug,
  studentName,
  studentEmail,
  temporaryPassword,
  loginUrl,
  supportEmail,
  supportPhone,
  dashboardUrl,
  welcomeMessage,
  customInstructions
}: CredentialsTemplateProps) {
  const defaultLoginUrl = tenantSlug && process.env.NEXT_PUBLIC_BASE_DOMAIN 
    ? `https://${tenantSlug}.${process.env.NEXT_PUBLIC_BASE_DOMAIN}/login`
    : loginUrl;

  const defaultDashboardUrl = tenantSlug && process.env.NEXT_PUBLIC_BASE_DOMAIN
    ? `https://${tenantSlug}.${process.env.NEXT_PUBLIC_BASE_DOMAIN}/home`
    : dashboardUrl;

  return (
    <BaseEmailTemplate
      academyName={academyName}
      academyLogo={academyLogo}
      primaryColor={primaryColor}
      secondaryColor={secondaryColor}
      previewText={`Suas credenciais de acesso - ${academyName}`}
    >
      <EmailHeading level={1} color={primaryColor}>
        🔑 Suas Credenciais de Acesso
      </EmailHeading>

      <EmailText>
        Olá <strong>{studentName}</strong>,
      </EmailText>

      <EmailText>
        Bem-vindo(a) à {academyName}! Sua conta foi criada com sucesso e você já pode 
        acessar nosso sistema online. Abaixo estão suas credenciais de acesso:
      </EmailText>

      {/* {welcomeMessage && (
        <div style={{
          backgroundColor: '#f0f9ff',
          padding: '20px',
          borderRadius: '8px',
          border: `1px solid ${primaryColor}20`,
          margin: '24px 0'
        }}>
          <EmailText color={primaryColor}>
            <em>"{welcomeMessage}"</em>
          </EmailText>
        </div>
      )} */}

      <EmailDivider color={primaryColor} />

      {/* Credenciais de acesso */}
      <div style={{
        backgroundColor: '#f8fafc',
        padding: '24px',
        borderRadius: '8px',
        border: `2px solid ${primaryColor}`,
        marginBottom: '24px'
      }}>
        <EmailHeading level={2} color={primaryColor}>
          🔐 Suas Credenciais
        </EmailHeading>

        <div style={{
          backgroundColor: '#ffffff',
          padding: '20px',
          borderRadius: '6px',
          border: '1px solid #e2e8f0',
          marginBottom: '16px'
        }}>
          <EmailText variant="small" color="#374151">
            <strong>E-mail de acesso:</strong>
          </EmailText>
          <div style={{
            backgroundColor: '#f1f5f9',
            padding: '12px',
            borderRadius: '4px',
            fontFamily: 'monospace',
            fontSize: '16px',
            color: '#1e293b',
            border: '1px solid #cbd5e1',
            marginBottom: '16px'
          }}>
            {studentEmail}
          </div>

          <EmailText variant="small" color="#374151">
            <strong>Senha temporária:</strong>
          </EmailText>
          <div style={{
            backgroundColor: '#fef3c7',
            padding: '12px',
            borderRadius: '4px',
            fontFamily: 'monospace',
            fontSize: '16px',
            color: '#92400e',
            border: '2px solid #f59e0b',
            fontWeight: 'bold'
          }}>
            {temporaryPassword}
          </div>
        </div>

        <div style={{
          backgroundColor: '#fef2f2',
          padding: '16px',
          borderRadius: '6px',
          border: '1px solid #fecaca'
        }}>
          <EmailText variant="small" color="#dc2626">
            <strong>⚠️ IMPORTANTE:</strong> Esta é uma senha temporária. Por motivos de segurança, 
            você deve alterá-la no seu primeiro acesso ao sistema.
          </EmailText>
        </div>
      </div>

      {/* Botão de acesso */}
      <div style={{ textAlign: 'center', margin: '32px 0' }}>
        <EmailButton href={defaultLoginUrl} primaryColor={primaryColor}>
          Fazer Login Agora
        </EmailButton>
      </div>

      <EmailDivider />

      {/* Instruções de primeiro acesso */}
      <EmailHeading level={2} color={primaryColor}>
        📋 Como Fazer Seu Primeiro Acesso
      </EmailHeading>

      <div style={{ marginBottom: '24px' }}>
        <EmailText>
          <strong>1. Clique no botão "Fazer Login Agora" acima</strong><br />
          Ou acesse diretamente: <a href={defaultLoginUrl} style={{ color: primaryColor }}>{defaultLoginUrl}</a>
        </EmailText>

        <EmailText>
          <strong>2. Digite suas credenciais</strong><br />
          Use o e-mail e a senha temporária fornecidos acima
        </EmailText>

        <EmailText>
          <strong>3. Altere sua senha</strong><br />
          No primeiro acesso, cria uma nova senha de acesso na pagina de configurações do seu perfil.
        </EmailText>

        <EmailText>
          <strong>4. Complete seu perfil</strong><br />
          Verifique e complete as informações do seu perfil se necessário
        </EmailText>

        {customInstructions && customInstructions.length > 0 && (
          <div style={{ marginTop: '20px' }}>
            <EmailText>
              <strong>5. Instruções adicionais:</strong>
            </EmailText>
            {customInstructions.map((instruction, index) => (
              <EmailText key={index} variant="small">
                • {instruction}
              </EmailText>
            ))}
          </div>
        )}
      </div>

      {/* Recursos disponíveis */}
      <div style={{
        backgroundColor: '#f0fdf4',
        padding: '20px',
        borderRadius: '8px',
        border: '1px solid #bbf7d0',
        marginBottom: '24px'
      }}>
        <EmailHeading level={3} color="#166534">
          🚀 O que você pode fazer no sistema:
        </EmailHeading>
        
        <div style={{ marginBottom: '16px' }}>
          <EmailText variant="small" color="#166534">
            • Visualizar seus horários de aula e treinos
          </EmailText>
          <EmailText variant="small" color="#166534">
            • Acompanhar seu progresso e evolução
          </EmailText>
          <EmailText variant="small" color="#166534">
            • Verificar informações de pagamento
          </EmailText>
          <EmailText variant="small" color="#166534">
            • Atualizar seus dados pessoais
          </EmailText>
          <EmailText variant="small" color="#166534">
            • Receber notificações importantes
          </EmailText>
        </div>
      </div>

      {/* Link para dashboard */}
      {defaultDashboardUrl && (
        <div style={{ textAlign: 'center', marginBottom: '24px' }}>
          <EmailText variant="small" color="#64748b">
            Após fazer login, acesse seu <a href={defaultDashboardUrl} style={{ color: primaryColor }}>painel principal</a> 
            para começar a usar todas as funcionalidades.
          </EmailText>
        </div>
      )}

      <EmailDivider />

      {/* Informações de suporte */}
      <EmailHeading level={3} color={primaryColor}>
        💬 Precisa de Ajuda?
      </EmailHeading>

      <EmailText>
        Se você tiver dificuldades para acessar o sistema ou qualquer dúvida, 
        nossa equipe está pronta para ajudar:
      </EmailText>

      <div style={{ marginBottom: '20px' }}>
        {supportEmail && (
          <EmailText variant="small" color="#64748b">
            📧 <strong>E-mail:</strong> <a href={`mailto:${supportEmail}`} style={{ color: primaryColor }}>{supportEmail}</a>
          </EmailText>
        )}
        {supportPhone && (
          <EmailText variant="small" color="#64748b">
            📞 <strong>Telefone:</strong> <a href={`tel:${supportPhone}`} style={{ color: primaryColor }}>{supportPhone}</a>
          </EmailText>
        )}
        {!supportEmail && !supportPhone && (
          <EmailText variant="small" color="#64748b">
            Entre em contato conosco através dos canais de atendimento da academia.
          </EmailText>
        )}
      </div>

      {/* Dicas de segurança */}
      <div style={{
        backgroundColor: '#fffbeb',
        padding: '16px',
        borderRadius: '6px',
        border: '1px solid #fed7aa',
        marginTop: '24px'
      }}>
        <EmailText variant="small" color="#d97706">
          <strong>🔒 Dicas de Segurança:</strong>
          <br />• Nunca compartilhe suas credenciais com outras pessoas
          <br />• Sempre faça logout ao usar computadores compartilhados
          <br />• Mantenha sua senha segura e única
          <br />• Entre em contato conosco se suspeitar de acesso não autorizado
        </EmailText>
      </div>

      {/* Mensagem final */}
      <EmailText variant="small" color="#64748b">
        Este e-mail contém informações confidenciais. Se você recebeu por engano, 
        por favor nos informe e delete esta mensagem.
      </EmailText>

      <div style={{
        backgroundColor: '#f0fdf4',
        padding: '16px',
        borderRadius: '6px',
        border: '1px solid #bbf7d0',
        marginTop: '24px'
      }}>
        <EmailText variant="small" color="#166534">
          🎉 <strong>Bem-vindo(a) à família {academyName}!</strong> Estamos animados 
          para acompanhar sua jornada conosco. Aproveite todas as funcionalidades 
          do nosso sistema!
        </EmailText>
      </div>
    </BaseEmailTemplate>
  );
}