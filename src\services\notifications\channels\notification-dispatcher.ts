/**
 * Dispatcher de notificações multi-canal
 * Coordena o envio de notificações através de diferentes canais
 */

import { NotificationChannelFactory } from './notification-channel-factory';
import { NotificationService } from '../core/notification-service';
import { NotificationPermissionService } from '../core/notification-permission-service';
import { NotificationLogger, NotificationError, NotificationErrorCodes } from '../core/notification-logger';
import type {
  NotificationChannel,
  NotificationType,
  NotificationCategory,
  NotificationPriority,
  CreateNotificationData
} from '../types/notification-types';
import type { NotificationChannelData, NotificationChannelResult } from './base/notification-channel';

export interface DispatchNotificationData {
  tenantId: string;
  userId: string;
  type: NotificationType;
  category: NotificationCategory;
  priority?: NotificationPriority;
  title: string;
  message: string;
  data?: Record<string, any>;
  channels?: NotificationChannel[];
  templateId?: string;
  variables?: Record<string, any>;
  scheduled_for?: string;
  expires_at?: string;
  recipientEmail?: string; // Email específico do destinatário (sobrescreve o email do userId)
}

export interface DispatchResult {
  success: boolean;
  partialSuccess: boolean; // Indica se pelo menos um canal foi bem-sucedido
  notificationId?: string;
  channelResults: Record<NotificationChannel, NotificationChannelResult>;
  errors: string[];
}

export class NotificationDispatcher {
  private notificationService: NotificationService;
  private permissionService: NotificationPermissionService;
  private logger: NotificationLogger;

  constructor() {
    this.notificationService = new NotificationService();
    this.permissionService = new NotificationPermissionService();
    this.logger = NotificationLogger.forComponent('NotificationDispatcher');
  }

  /**
   * Envia uma notificação através de múltiplos canais
   * Centraliza verificações de permissões e configurações
   */
  async dispatch(data: DispatchNotificationData): Promise<DispatchResult> {
    const context = NotificationLogger.createNotificationContext({
      tenantId: data.tenantId,
      userId: data.userId,
      notificationType: data.type
    });

    this.logger.startOperation('dispatch notification', context);
    const startTime = Date.now();

    const channelResults: Record<string, NotificationChannelResult> = {};
    const errors: string[] = [];
    let notificationId: string | undefined;

    try {
      // 1. Criar notificação in-app no banco de dados
      const notificationData: CreateNotificationData = {
        user_id: data.userId,
        type: data.type,
        category: data.category,
        priority: data.priority || 'medium',
        title: data.title,
        message: data.message,
        data: data.data,
        channels: data.channels || ['in_app'],
        scheduled_for: data.scheduled_for,
        expires_at: data.expires_at
      };

      const createResult = await this.notificationService.create(data.tenantId, notificationData);
      
      if (!createResult.success) {
        errors.push(`Erro ao criar notificação: ${createResult.error}`);
        return {
          success: false,
          partialSuccess: false,
          channelResults: channelResults as Record<NotificationChannel, NotificationChannelResult>,
          errors
        };
      }

      notificationId = createResult.data?.id;

      // 2. Determinar canais permitidos (sempre verificar permissões)
      const requestedChannels = data.channels || ['in_app', 'email'];
      const channels = await this.validateAndGetPermittedChannels(
        data.tenantId,
        data.userId,
        data.type,
        requestedChannels
      );

      // 3. Preparar dados para os canais
      const channelData: NotificationChannelData = {
        tenantId: data.tenantId,
        userId: data.userId,
        type: data.type,
        title: data.title,
        message: data.message,
        data: data.data,
        templateId: data.templateId,
        variables: data.variables,
        recipientEmail: data.recipientEmail
      };

      // 4. Verificar se há canais permitidos
      if (channels.length === 0) {
        const error = new NotificationError(
          'Nenhum canal de notificação permitido para este usuário/tipo',
          NotificationErrorCodes.PERMISSION_DENIED,
          context
        );
        this.logger.failOperation('dispatch notification', error, context);
        errors.push(error.message);
        return {
          success: false,
          partialSuccess: false,
          notificationId,
          channelResults: channelResults as Record<NotificationChannel, NotificationChannelResult>,
          errors
        };
      }

      // 5. Enviar através de cada canal permitido
      for (const channelType of channels) {
        const channelContext = { ...context, channel: channelType };
        this.logger.debug('Sending to channel', channelContext);

        try {
          // Pular canal in-app pois já foi criado no banco
          if (channelType === 'in_app') {
            channelResults[channelType] = {
              success: true,
              messageId: notificationId
            };
            this.logger.info('In-app notification created', { ...channelContext, messageId: notificationId });
            continue;
          }

          // Obter canal e enviar
          const channel = await NotificationChannelFactory.getChannel(channelType);
          const result = await channel.send(channelData);

          channelResults[channelType] = result;

          if (result.success) {
            this.logger.info('Channel send successful', { ...channelContext, messageId: result.messageId });
          } else {
            this.logger.warn('Channel send failed', { ...channelContext, error: result.error });
            errors.push(`Erro no canal ${channelType}: ${result.error}`);
          }

        } catch (error) {
          const notificationError = new NotificationError(
            `Erro inesperado no canal ${channelType}`,
            NotificationErrorCodes.INTERNAL_ERROR,
            channelContext,
            error instanceof Error ? error : undefined
          );

          this.logger.error('Channel send error', channelContext, notificationError);
          errors.push(notificationError.message);
          channelResults[channelType] = {
            success: false,
            error: notificationError.message
          };
        }
      }

      // 6. Determinar sucesso geral
      const hasSuccessfulChannel = Object.values(channelResults).some(result => result.success);
      const duration = Date.now() - startTime;

      if (hasSuccessfulChannel) {
        this.logger.endOperation('dispatch notification', {
          ...context,
          notificationId,
          channelsUsed: channels,
          successfulChannels: Object.keys(channelResults).filter(ch => channelResults[ch].success)
        }, duration);
      } else {
        this.logger.error('All channels failed', { ...context, errors });
      }

      return {
        success: hasSuccessfulChannel,
        partialSuccess: hasSuccessfulChannel,
        notificationId,
        channelResults: channelResults as Record<NotificationChannel, NotificationChannelResult>,
        errors
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      const notificationError = new NotificationError(
        'Erro geral no dispatch de notificação',
        NotificationErrorCodes.INTERNAL_ERROR,
        context,
        error instanceof Error ? error : undefined
      );

      this.logger.failOperation('dispatch notification', notificationError, context);
      this.logger.performance('dispatch notification (failed)', duration, context);

      errors.push(notificationError.message);

      return {
        success: false,
        partialSuccess: false,
        notificationId,
        channelResults: channelResults as Record<NotificationChannel, NotificationChannelResult>,
        errors
      };
    }
  }

  /**
   * Centraliza verificação de permissões e configurações para um usuário/tipo
   * Este método é o ponto único de verificação de permissões
   */
  async validateAndGetPermittedChannels(
    tenantId: string,
    userId: string,
    type: NotificationType,
    requestedChannels: NotificationChannel[]
  ): Promise<NotificationChannel[]> {
    const context = NotificationLogger.createNotificationContext({
      tenantId,
      userId,
      notificationType: type
    });

    this.logger.debug('Validating channel permissions', { ...context, requestedChannels });

    try {
      // Verificar permissões através do serviço centralizado
      const permittedChannels = await this.permissionService.getPermittedChannels(
        tenantId,
        userId,
        type,
        requestedChannels
      );

      this.logger.info('Channel validation completed', {
        ...context,
        requestedChannels,
        permittedChannels
      });

      return permittedChannels;
    } catch (error) {
      const notificationError = new NotificationError(
        'Erro ao validar permissões de canais',
        NotificationErrorCodes.PERMISSION_DENIED,
        context,
        error instanceof Error ? error : undefined
      );

      this.logger.error('Channel validation failed', context, notificationError);
      throw notificationError;
    }
  }

  /**
   * Envia múltiplas notificações em lote de forma otimizada
   * Agrupa por canal e tenant para maximizar eficiência
   */
  async dispatchBatch(notifications: DispatchNotificationData[]): Promise<DispatchResult[]> {
    const results: DispatchResult[] = [];
    const errors: string[] = [];

    try {
      // 1. Criar todas as notificações in-app no banco primeiro
      const notificationIds: (string | undefined)[] = [];

      for (const data of notifications) {
        try {
          const notificationData: CreateNotificationData = {
            user_id: data.userId,
            type: data.type,
            category: data.category,
            priority: data.priority || 'medium',
            title: data.title,
            message: data.message,
            data: data.data,
            channels: data.channels || ['in_app'],
            scheduled_for: data.scheduled_for,
            expires_at: data.expires_at
          };

          const createResult = await this.notificationService.create(data.tenantId, notificationData);

          if (createResult.success) {
            notificationIds.push(createResult.data?.id);
          } else {
            notificationIds.push(undefined);
            errors.push(`Erro ao criar notificação para usuário ${data.userId}: ${createResult.error}`);
          }
        } catch (error) {
          notificationIds.push(undefined);
          const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
          errors.push(`Erro ao criar notificação para usuário ${data.userId}: ${errorMessage}`);
        }
      }

      // 2. Agrupar notificações por canal e tenant para otimizar envio
      const channelGroups = new Map<string, Map<string, { notifications: DispatchNotificationData[], indices: number[] }>>();

      for (let i = 0; i < notifications.length; i++) {
        const notification = notifications[i];
        const channels = notification.channels || ['in_app'];

        for (const channelType of channels) {
          if (channelType === 'in_app') continue; // Já processado

          const channelKey = channelType;
          const tenantKey = notification.tenantId;

          if (!channelGroups.has(channelKey)) {
            channelGroups.set(channelKey, new Map());
          }

          const tenantGroups = channelGroups.get(channelKey)!;
          if (!tenantGroups.has(tenantKey)) {
            tenantGroups.set(tenantKey, { notifications: [], indices: [] });
          }

          tenantGroups.get(tenantKey)!.notifications.push(notification);
          tenantGroups.get(tenantKey)!.indices.push(i);
        }
      }

      // 3. Inicializar resultados com dados in-app
      for (let i = 0; i < notifications.length; i++) {
        const notification = notifications[i];
        const notificationId = notificationIds[i];
        const channels = notification.channels || ['in_app'];

        const channelResults = {} as Record<NotificationChannel, NotificationChannelResult>;

        // Resultado in-app
        if (channels.includes('in_app')) {
          (channelResults as any)['in_app'] = {
            success: !!notificationId,
            messageId: notificationId
          };
        }

        results.push({
          success: !!notificationId,
          partialSuccess: !!notificationId,
          notificationId,
          channelResults,
          errors: []
        });
      }

      // 4. Processar cada canal em lote
      for (const [channelType, tenantGroups] of channelGroups) {
        try {
          const channel = await NotificationChannelFactory.getChannel(channelType as NotificationChannel);

          for (const [, groupData] of tenantGroups) {
            const { notifications: tenantNotifications, indices } = groupData;

            // Verificar permissões para cada notificação
            const permittedNotifications: { notification: DispatchNotificationData, index: number }[] = [];

            for (let j = 0; j < tenantNotifications.length; j++) {
              const notification = tenantNotifications[j];
              const originalIndex = indices[j];

              try {
                const permittedChannels = await this.permissionService.getPermittedChannels(
                  notification.tenantId,
                  notification.userId,
                  notification.type,
                  [channelType as NotificationChannel]
                );

                if (permittedChannels.includes(channelType as NotificationChannel)) {
                  permittedNotifications.push({ notification, index: originalIndex });
                } else {
                  (results[originalIndex].channelResults as any)[channelType] = {
                    success: false,
                    error: 'Canal não permitido para este usuário'
                  };
                }
              } catch (permError) {
                const errorMessage = permError instanceof Error ? permError.message : 'Erro desconhecido';
                (results[originalIndex].channelResults as any)[channelType] = {
                  success: false,
                  error: `Erro ao verificar permissões: ${errorMessage}`
                };
              }
            }

            // Enviar em lote para este tenant/canal
            if (permittedNotifications.length > 0) {
              const channelData = permittedNotifications.map(({ notification }) => ({
                tenantId: notification.tenantId,
                userId: notification.userId,
                type: notification.type,
                title: notification.title,
                message: notification.message,
                data: notification.data,
                templateId: notification.templateId,
                variables: notification.variables,
                recipientEmail: notification.recipientEmail
              }));

              const batchResult = await channel.sendBatch(channelData);

              console.log(`🔍 [DEBUG] Batch result for channel ${channelType}:`, {
                expectedResults: permittedNotifications.length,
                actualResults: batchResult.results.length,
                success: batchResult.success,
                totalSent: batchResult.totalSent,
                totalFailed: batchResult.totalFailed
              });

              // Mapear resultados de volta para as notificações originais
              for (let j = 0; j < permittedNotifications.length; j++) {
                const { index: originalIndex } = permittedNotifications[j];
                const channelResult = batchResult.results[j] || {
                  success: false,
                  error: 'Resultado não encontrado'
                };

                if (!batchResult.results[j]) {
                  console.log(`⚠️ [DEBUG] Missing result at index ${j} for channel ${channelType}`, {
                    originalIndex,
                    totalResults: batchResult.results.length,
                    expectedIndex: j
                  });
                }

                (results[originalIndex].channelResults as any)[channelType] = channelResult;

                if (!channelResult.success && channelResult.error) {
                  results[originalIndex].errors.push(`Erro no canal ${channelType}: ${channelResult.error}`);
                }
              }
            }
          }
        } catch (channelError) {
          const errorMessage = channelError instanceof Error ? channelError.message : 'Erro desconhecido';

          // Marcar todas as notificações deste canal como falha
          for (const [, tenantGroups] of channelGroups.get(channelType)!) {
            for (const index of tenantGroups.indices) {
              (results[index].channelResults as any)[channelType] = {
                success: false,
                error: `Erro no canal ${channelType}: ${errorMessage}`
              };
              results[index].errors.push(`Erro no canal ${channelType}: ${errorMessage}`);
            }
          }
        }
      }

      // 5. Atualizar status de sucesso geral para cada resultado
      for (const result of results) {
        const channelSuccesses = Object.values(result.channelResults).map(r => r.success);
        const hasAnySuccess = channelSuccesses.length > 0 && channelSuccesses.some(success => success);
        const hasAllSuccess = channelSuccesses.length > 0 && channelSuccesses.every(success => success);

        result.partialSuccess = hasAnySuccess;
        // Considerar sucesso total apenas se todos os canais solicitados foram bem-sucedidos
        result.success = hasAllSuccess;
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';

      // Em caso de erro geral, retornar falha para todas as notificações
      return notifications.map(() => ({
        success: false,
        partialSuccess: false,
        channelResults: {} as Record<NotificationChannel, NotificationChannelResult>,
        errors: [`Erro geral no lote: ${errorMessage}`]
      }));
    }

    return results;
  }

  /**
   * Envia notificação de pagamento
   */
  async sendPaymentReminder(data: {
    tenantId: string;
    userId: string;
    studentName: string;
    amount: number;
    dueDate: string;
    planName: string;
    paymentMethod?: string;
    invoiceUrl?: string;
    paymentId?: string;
    channels?: NotificationChannel[];
  }): Promise<DispatchResult> {
    console.log('🔍 [DEBUG] NotificationDispatcher.sendPaymentReminder() iniciado');
    console.log('🔍 [DEBUG] Dados recebidos:', {
      tenantId: data.tenantId,
      userId: data.userId,
      studentName: data.studentName,
      amount: data.amount,
      dueDate: data.dueDate,
      planName: data.planName,
      channels: data.channels
    });

    // Determinar canais solicitados (padrão: in_app e email)
    const requestedChannels = data.channels || ['in_app', 'email'];
    console.log('🔍 [DEBUG] Canais solicitados:', requestedChannels);

    // Verificar canais permitidos baseado nas configurações
    console.log('🔍 [DEBUG] Verificando canais permitidos...');
    const permittedChannels = await this.permissionService.getPermittedChannels(
      data.tenantId,
      data.userId,
      'payment',
      requestedChannels
    );
    console.log('🔍 [DEBUG] Canais permitidos:', permittedChannels);

    return await this.dispatch({
      tenantId: data.tenantId,
      userId: data.userId,
      type: 'payment',
      category: 'reminder',
      priority: 'high',
      title: 'Lembrete de Pagamento',
      message: `Sua mensalidade de ${data.planName} vence em breve.`,
      channels: permittedChannels,
      templateId: 'payment_reminder',
      variables: {
        studentName: data.studentName,
        amount: data.amount,
        dueDate: data.dueDate,
        planName: data.planName,
        paymentMethod: data.paymentMethod,
        invoiceUrl: data.invoiceUrl,
        paymentId: data.paymentId
      }
    });
  }

  /**
   * Envia notificação de aula
   */
  async sendClassReminder(data: {
    tenantId: string;
    userId: string;
    studentName: string;
    className: string;
    instructorName: string;
    classDate: string;
    classTime: string;
    location?: string;
    duration?: number;
    channels?: NotificationChannel[];
    reminderType?: 'upcoming' | 'today' | 'starting_soon';
  }): Promise<DispatchResult> {
    // Determinar canais solicitados (padrão: in_app e email)
    const requestedChannels = data.channels || ['in_app', 'email'];

    // Verificar canais permitidos baseado nas configurações
    const permittedChannels = await this.permissionService.getPermittedChannels(
      data.tenantId,
      data.userId,
      'class',
      requestedChannels
    );

    return await this.dispatch({
      tenantId: data.tenantId,
      userId: data.userId,
      type: 'class',
      category: 'reminder',
      priority: data.reminderType === 'starting_soon' ? 'urgent' : 'medium',
      title: 'Lembrete de Aula',
      message: `Você tem uma aula de ${data.className} agendada.`,
      channels: permittedChannels,
      templateId: 'class_reminder',
      variables: {
        studentName: data.studentName,
        className: data.className,
        instructorName: data.instructorName,
        classDate: data.classDate,
        classTime: data.classTime,
        location: data.location,
        duration: data.duration,
        reminderType: data.reminderType || 'upcoming'
      }
    });
  }

  /**
   * Envia notificação do sistema
   */
  async sendSystemNotification(data: {
    tenantId: string;
    userId: string;
    title: string;
    message: string;
    category?: NotificationCategory;
    priority?: NotificationPriority;
    channels?: NotificationChannel[];
  }): Promise<DispatchResult> {
    return await this.dispatch({
      tenantId: data.tenantId,
      userId: data.userId,
      type: 'system',
      category: data.category || 'info',
      priority: data.priority || 'medium',
      title: data.title,
      message: data.message,
      channels: data.channels || ['in_app']
    });
  }

  /**
   * Envia credenciais de acesso por e-mail
   */
  async sendCredentials(data: {
    tenantId: string;
    userId: string;
    studentName: string;
    studentEmail: string;
    temporaryPassword: string;
    loginUrl?: string;
    supportEmail?: string;
    supportPhone?: string;
    welcomeMessage?: string;
    customInstructions?: string[];
  }): Promise<DispatchResult> {
    const context = NotificationLogger.createNotificationContext({
      tenantId: data.tenantId,
      userId: data.userId,
      notificationType: 'system'
    });

    this.logger.startOperation('send credentials', context);
    const startTime = Date.now();

    try {
      // Validar dados obrigatórios
      if (!data.tenantId || !data.userId || !data.studentEmail || !data.temporaryPassword) {
        const error = new NotificationError(
          'Dados obrigatórios faltando para envio de credenciais',
          NotificationErrorCodes.MISSING_DATA,
          context
        );
        this.logger.failOperation('send credentials', error, context);
        return {
          success: false,
          partialSuccess: false,
          channelResults: {} as Record<NotificationChannel, NotificationChannelResult>,
          errors: [error.message]
        };
      }

      // Validar formato do e-mail
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(data.studentEmail)) {
        const error = new NotificationError(
          'Formato de e-mail inválido',
          NotificationErrorCodes.INVALID_DATA,
          context
        );
        this.logger.failOperation('send credentials', error, context);
        return {
          success: false,
          partialSuccess: false,
          channelResults: {} as Record<NotificationChannel, NotificationChannelResult>,
          errors: [error.message]
        };
      }

      console.log('🔍 [DEBUG] NotificationDispatcher.sendCredentials() iniciado');
      console.log('🔍 [DEBUG] Dados validados:', {
        tenantId: data.tenantId,
        userId: data.userId,
        studentName: data.studentName,
        studentEmail: data.studentEmail,
        hasPassword: !!data.temporaryPassword,
        loginUrl: data.loginUrl
      });

      // Forçar apenas canal de e-mail para credenciais (por segurança)
      const channels: NotificationChannel[] = ['email'];
      console.log('🔍 [DEBUG] Canais forçados para credenciais:', channels);

      // Verificar canais permitidos com tratamento de erro
      let permittedChannels: NotificationChannel[];
      try {
        console.log('🔍 [DEBUG] Verificando canais permitidos...');
        permittedChannels = await this.permissionService.getPermittedChannels(
          data.tenantId,
          data.userId,
          'system', // Usar 'system' como tipo para credenciais
          channels
        );
        console.log('🔍 [DEBUG] Canais permitidos:', permittedChannels);
      } catch (permissionError) {
        this.logger.warn('Permission check failed, forcing email channel', context, permissionError instanceof Error ? permissionError : undefined);
        permittedChannels = ['email']; // Forçar e-mail em caso de erro de permissão
      }

      // Se e-mail não estiver permitido, forçar mesmo assim (credenciais são críticas)
      if (!permittedChannels.includes('email')) {
        console.warn('⚠️ [WARNING] Canal de e-mail não permitido para credenciais, mas forçando envio...');
        permittedChannels = ['email'];
      }

      const result = await this.dispatch({
        tenantId: data.tenantId,
        userId: data.userId,
        type: 'system',
        category: 'info',
        priority: 'high',
        title: 'Suas Credenciais de Acesso',
        message: `Bem-vindo(a) ${data.studentName}! Suas credenciais de acesso foram criadas.`,
        channels: permittedChannels,
        templateId: 'credentials',
        variables: {
          studentName: data.studentName,
          studentEmail: data.studentEmail,
          temporaryPassword: data.temporaryPassword,
          loginUrl: data.loginUrl || `https://${process.env.NEXT_PUBLIC_BASE_DOMAIN}/login`,
          supportEmail: data.supportEmail,
          supportPhone: data.supportPhone,
          welcomeMessage: data.welcomeMessage,
          customInstructions: data.customInstructions
        },
        recipientEmail: data.studentEmail // Garantir que o e-mail seja enviado para o aluno
      });

      const duration = Date.now() - startTime;
      
      if (result.success || result.partialSuccess) {
        this.logger.endOperation('send credentials', { ...context, credentialsSent: result.success }, duration);
      } else {
        this.logger.error('Credentials send failed', { ...context, errors: result.errors });
      }

      return result;

    } catch (error) {
      const duration = Date.now() - startTime;
      const notificationError = new NotificationError(
        'Erro interno ao enviar credenciais',
        NotificationErrorCodes.INTERNAL_ERROR,
        context,
        error instanceof Error ? error : undefined
      );

      this.logger.failOperation('send credentials', notificationError, context);
      this.logger.performance('send credentials (failed)', duration, context);

      return {
        success: false,
        partialSuccess: false,
        channelResults: {} as Record<NotificationChannel, NotificationChannelResult>,
        errors: [notificationError.message]
      };
    }
  }

  /**
   * Verifica o status de entrega de uma notificação
   */
  async getDeliveryStatus(notificationId: string, channelType: NotificationChannel): Promise<{
    success: boolean;
    status?: string;
    error?: string;
  }> {
    try {
      const channel = await NotificationChannelFactory.getChannel(channelType);
      const status = await channel.getDeliveryStatus(notificationId);
      
      return {
        success: true,
        status: status.status
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  }
}
