# Design Document

## Overview

O sistema de rastreamento de histórico de status de usuários será implementado através de uma nova tabela `user_status_history` que registrará automaticamente todas as mudanças de status através de triggers de banco de dados. O sistema será otimizado para consultas rápidas de métricas e manterá compatibilidade com a arquitetura multi-tenant existente.

## Architecture

### Database Schema Design

#### Nova Tabela: user_status_history

```sql
CREATE TABLE user_status_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id),
    branch_id UUID NOT NULL REFERENCES branches(id),
    user_id UUID NOT NULL REFERENCES auth.users(id),
    old_status TEXT,
    new_status TEXT NOT NULL,
    changed_at TIMESTAMPTZ WITH TIME ZONE NOT NULL DEFAULT NOW(),
    changed_by UUID REFERENCES auth.users(id),
    reason TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ WITH TIME ZONE NOT NULL DEFAULT NOW()
);
```

#### Índices Otimizados

```sql
-- Índice principal para consultas por tenant e data
CREATE INDEX idx_user_status_history_tenant_date 
ON user_status_history (tenant_id, changed_at DESC);

-- Índice para consultas por branch e data
CREATE INDEX idx_user_status_history_branch_date 
ON user_status_history (branch_id, changed_at DESC);

-- Índice para consultas por usuário específico
CREATE INDEX idx_user_status_history_user_date 
ON user_status_history (user_id, changed_at DESC);

-- Índice composto para métricas por tenant, status e data
CREATE INDEX idx_user_status_history_metrics 
ON user_status_history (tenant_id, new_status, changed_at DESC);
```

### Trigger System Design

#### Trigger Function

```sql
CREATE OR REPLACE FUNCTION track_user_status_changes()
RETURNS TRIGGER AS $$
BEGIN
    -- Verificar se o status realmente mudou
    IF OLD.status IS DISTINCT FROM NEW.status THEN
        INSERT INTO user_status_history (
            tenant_id,
            branch_id,
            user_id,
            old_status,
            new_status,
            changed_at,
            changed_by,
            reason,
            metadata
        ) VALUES (
            NEW.tenant_id,
            NEW.branch_id,
            NEW.id,
            OLD.status,
            NEW.status,
            NOW(),
            -- Tentar obter o usuário atual da sessão
            COALESCE(
                (SELECT auth.uid()),
                NEW.id -- Fallback para o próprio usuário
            ),
            -- Extrair motivo dos metadados se disponível
            COALESCE(
                (NEW.metadata->>'status_change_reason')::TEXT,
                NULL
            ),
            jsonb_build_object(
                'previous_updated_at', OLD.updated_at,
                'new_updated_at', NEW.updated_at,
                'change_source', 'trigger'
            )
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

#### Trigger Creation

```sql
CREATE TRIGGER trigger_user_status_history
    AFTER UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION track_user_status_changes();
```

## Components and Interfaces

### Database Functions (RPC)

#### 1. Função para Métricas de Usuários com Histórico

```sql
CREATE OR REPLACE FUNCTION get_users_metrics_with_history(
    p_tenant_id UUID,
    p_branch_id UUID DEFAULT NULL
)
RETURNS TABLE (
    active_users_now INTEGER,
    active_users_last_month INTEGER,
    new_users_now INTEGER,
    new_users_last_month INTEGER
) AS $$
DECLARE
    current_date_br DATE;
    start_current_month DATE;
    end_current_month DATE;
    start_last_month DATE;
    end_last_month DATE;
BEGIN
    -- Calcular datas no timezone do Brasil
    current_date_br := (NOW() AT TIME ZONE 'America/Sao_Paulo')::DATE;
    start_current_month := DATE_TRUNC('month', current_date_br);
    end_current_month := (DATE_TRUNC('month', current_date_br) + INTERVAL '1 month - 1 day')::DATE;
    start_last_month := (DATE_TRUNC('month', current_date_br) - INTERVAL '1 month')::DATE;
    end_last_month := (DATE_TRUNC('month', current_date_br) - INTERVAL '1 day')::DATE;
    
    RETURN QUERY
    WITH user_status_at_dates AS (
        SELECT DISTINCT ON (u.id, date_point)
            u.id as user_id,
            date_point,
            COALESCE(
                (SELECT ush.new_status 
                 FROM user_status_history ush 
                 WHERE ush.user_id = u.id 
                   AND ush.tenant_id = p_tenant_id
                   AND (p_branch_id IS NULL OR ush.branch_id = p_branch_id)
                   AND ush.changed_at::DATE <= date_point
                 ORDER BY ush.changed_at DESC 
                 LIMIT 1),
                u.status
            ) as status_at_date,
            u.created_at::DATE as user_created_date
        FROM users u
        CROSS JOIN (
            VALUES 
                (current_date_br),
                (end_last_month)
        ) AS dates(date_point)
        WHERE u.tenant_id = p_tenant_id
          AND (p_branch_id IS NULL OR u.branch_id = p_branch_id)
          AND u.created_at::DATE <= date_point
    )
    SELECT 
        -- Usuários ativos agora
        (SELECT COUNT(*)::INTEGER 
         FROM user_status_at_dates 
         WHERE date_point = current_date_br 
           AND status_at_date = 'active'),
        
        -- Usuários ativos no final do mês passado
        (SELECT COUNT(*)::INTEGER 
         FROM user_status_at_dates 
         WHERE date_point = end_last_month 
           AND status_at_date = 'active'),
        
        -- Novos usuários este mês
        (SELECT COUNT(*)::INTEGER 
         FROM user_status_at_dates 
         WHERE date_point = current_date_br 
           AND status_at_date = 'active'
           AND user_created_date >= start_current_month),
        
        -- Novos usuários mês passado
        (SELECT COUNT(*)::INTEGER 
         FROM user_status_at_dates 
         WHERE date_point = end_last_month 
           AND status_at_date = 'active'
           AND user_created_date >= start_last_month 
           AND user_created_date <= end_last_month);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

#### 2. Função para Dados de Gráfico Diário

```sql
CREATE OR REPLACE FUNCTION get_daily_users_chart_data_with_history(
    p_tenant_id UUID,
    p_branch_id UUID DEFAULT NULL,
    p_days INTEGER DEFAULT 30
)
RETURNS TABLE (
    day_date DATE,
    active_count INTEGER,
    inactive_count INTEGER,
    suspended_count INTEGER
) AS $$
DECLARE
    start_date DATE;
    end_date DATE;
BEGIN
    -- Calcular período no timezone do Brasil
    end_date := (NOW() AT TIME ZONE 'America/Sao_Paulo')::DATE;
    start_date := end_date - (p_days - 1);
    
    RETURN QUERY
    WITH date_series AS (
        SELECT generate_series(start_date, end_date, '1 day'::INTERVAL)::DATE as day_date
    ),
    user_status_by_day AS (
        SELECT DISTINCT ON (u.id, ds.day_date)
            u.id as user_id,
            ds.day_date,
            COALESCE(
                (SELECT ush.new_status 
                 FROM user_status_history ush 
                 WHERE ush.user_id = u.id 
                   AND ush.tenant_id = p_tenant_id
                   AND (p_branch_id IS NULL OR ush.branch_id = p_branch_id)
                   AND ush.changed_at::DATE <= ds.day_date
                 ORDER BY ush.changed_at DESC 
                 LIMIT 1),
                u.status
            ) as status_at_date
        FROM users u
        CROSS JOIN date_series ds
        WHERE u.tenant_id = p_tenant_id
          AND (p_branch_id IS NULL OR u.branch_id = p_branch_id)
          AND u.created_at::DATE <= ds.day_date
    )
    SELECT 
        usbd.day_date,
        COUNT(CASE WHEN usbd.status_at_date = 'active' THEN 1 END)::INTEGER as active_count,
        COUNT(CASE WHEN usbd.status_at_date = 'inactive' THEN 1 END)::INTEGER as inactive_count,
        COUNT(CASE WHEN usbd.status_at_date = 'suspended' THEN 1 END)::INTEGER as suspended_count
    FROM user_status_by_day usbd
    GROUP BY usbd.day_date
    ORDER BY usbd.day_date;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### Application Layer Updates

#### 1. Atualização das Actions de Métricas

```typescript
// src/app/(dashboard)/dashboard/actions/metrics-actions.ts

export async function getStudentsMetricsWithHistory(tenantId: string, branchId?: string): Promise<StudentsMetrics> {
  const supabase = await createClient()

  try {
    const { data, error } = await supabase.rpc('get_users_metrics_with_history', {
      p_tenant_id: tenantId,
      p_branch_id: branchId || null
    })

    if (error) throw error

    if (data && data.length > 0) {
      const metrics = data[0]
      return {
        activeStudentsNow: metrics.active_users_now || 0,
        activeStudentsLast: metrics.active_users_last_month || 0,
        newStudentsNow: metrics.new_users_now || 0,
        newStudentsLast: metrics.new_users_last_month || 0
      }
    }

    return {
      activeStudentsNow: 0,
      activeStudentsLast: 0,
      newStudentsNow: 0,
      newStudentsLast: 0
    }
  } catch (error) {
    console.error('Error fetching students metrics with history:', error)
    return {
      activeStudentsNow: 0,
      activeStudentsLast: 0,
      newStudentsNow: 0,
      newStudentsLast: 0
    }
  }
}
```

#### 2. Função para Dados de Gráfico

```typescript
export async function getStudentsChartDataWithHistory(
  tenantId: string, 
  branchId?: string, 
  days: number = 30
): Promise<StudentsChartData[]> {
  const supabase = await createClient()

  try {
    const { data, error } = await supabase.rpc('get_daily_users_chart_data_with_history', {
      p_tenant_id: tenantId,
      p_branch_id: branchId || null,
      p_days: days
    })

    if (error) throw error

    if (data && data.length > 0) {
      return data.map((row: any) => ({
        date: new Date(row.day_date + 'T12:00:00.000Z').toLocaleDateString('pt-BR', {
          day: '2-digit',
          month: '2-digit',
          timeZone: 'America/Sao_Paulo'
        }),
        activeStudents: row.active_count || 0,
        inactiveStudents: row.inactive_count || 0,
        suspendedStudents: row.suspended_count || 0
      }))
    }

    return []
  } catch (error) {
    console.error('Error fetching students chart data with history:', error)
    return []
  }
}
```

## Data Models

### UserStatusHistory Interface

```typescript
export interface UserStatusHistory {
  id: string
  tenant_id: string
  branch_id: string
  user_id: string
  old_status: 'active' | 'inactive' | 'suspended' | null
  new_status: 'active' | 'inactive' | 'suspended'
  changed_at: string
  changed_by?: string
  reason?: string
  metadata: Record<string, any>
  created_at: string
}
```

### Extended StudentsChartData

```typescript
export interface StudentsChartData {
  date: string
  activeStudents: number
  inactiveStudents: number
  suspendedStudents?: number // Nova propriedade opcional
}
```

## Error Handling

### Database Level

1. **Trigger Failures**: Se o trigger falhar, a transação de atualização do usuário será revertida
2. **Constraint Violations**: Validações de integridade referencial impedirão dados inconsistentes
3. **Performance Issues**: Índices otimizados garantem performance mesmo com grandes volumes

### Application Level

1. **RPC Function Errors**: Fallback para consultas diretas se funções RPC falharem
2. **Data Validation**: Validação de tipos TypeScript para garantir consistência
3. **Graceful Degradation**: Sistema continua funcionando mesmo se histórico não estiver disponível

## Testing Strategy

### Database Testing

1. **Trigger Testing**: Verificar se mudanças de status geram registros corretos
2. **Performance Testing**: Testar consultas com volumes grandes de dados
3. **Data Integrity**: Verificar integridade referencial e constraints

### Integration Testing

1. **API Testing**: Testar endpoints que usam as novas funções
2. **Dashboard Testing**: Verificar se gráficos exibem dados corretos
3. **Multi-tenant Testing**: Garantir isolamento correto entre tenants

### Migration Testing

1. **Data Migration**: Testar migração de dados existentes
2. **Rollback Testing**: Verificar possibilidade de reverter mudanças
3. **Performance Impact**: Medir impacto na performance durante migração

## Migration Strategy

### Phase 1: Database Schema
1. Criar tabela `user_status_history`
2. Criar índices otimizados
3. Criar função trigger
4. Criar trigger na tabela users

### Phase 2: Data Migration
1. Popular histórico inicial baseado em dados existentes
2. Validar integridade dos dados migrados
3. Testar triggers com dados reais

### Phase 3: Application Updates
1. Criar novas funções RPC otimizadas
2. Atualizar actions de métricas
3. Atualizar componentes de dashboard
4. Implementar testes

### Phase 4: Deployment & Monitoring
1. Deploy em ambiente de staging
2. Testes de performance e carga
3. Deploy em produção
4. Monitoramento de performance e erros