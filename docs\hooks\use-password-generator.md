# usePasswordGenerator Hook

Hook React para geração de senhas aleatórias seguras usando algoritmos criptograficamente seguros.

## Características

- ✅ Geração criptograficamente segura usando `crypto.getRandomValues()`
- ✅ Configuração flexível de caracteres e comprimento
- ✅ Validação automática de critérios de segurança
- ✅ Cálculo de entropia e força da senha
- ✅ Exclusão de caracteres similares (opcional)
- ✅ Fallback robusto para casos de erro
- ✅ Limpeza de dados sensíveis da memória

## Uso Básico

```typescript
import { usePasswordGenerator } from '@/hooks';

function MyComponent() {
  const { generatePassword, isGenerating, lastGenerated } = usePasswordGenerator();

  const handleGeneratePassword = () => {
    const result = generatePassword();
    console.log('Senha gerada:', result.password);
    console.log('Força:', result.strength);
    console.log('Entropia:', result.entropy);
  };

  return (
    <div>
      <button onClick={handleGeneratePassword} disabled={isGenerating}>
        {isGenerating ? 'Gerando...' : 'Gerar Senha'}
      </button>
      
      {lastGenerated && (
        <div>
          <p>Senha: {lastGenerated.password}</p>
          <p>Força: {lastGenerated.strength}</p>
          <p>Entropia: {lastGenerated.entropy} bits</p>
        </div>
      )}
    </div>
  );
}
```

## Configuração Personalizada

```typescript
const { generatePassword } = usePasswordGenerator();

// Senha de 16 caracteres com configuração personalizada
const result = generatePassword({
  length: 16,
  includeUppercase: true,
  includeLowercase: true,
  includeNumbers: true,
  includeSpecialChars: false, // Sem caracteres especiais
  excludeSimilar: true        // Excluir 0, O, 1, l, I, etc.
});
```

## Uso em Server Actions

Para uso em server actions ou contextos não-React, use a função `generatePasswordSync`:

```typescript
import { generatePasswordSync } from '@/hooks';

export async function createUser(userData: any) {
  // Gerar senha segura
  const passwordResult = generatePasswordSync({
    length: 12,
    includeUppercase: true,
    includeLowercase: true,
    includeNumbers: true,
    includeSpecialChars: true,
    excludeSimilar: true
  });

  // Usar a senha gerada
  const user = await createUserWithPassword({
    ...userData,
    password: passwordResult.password
  });

  // Limpar dados sensíveis
  clearSensitiveData(passwordResult.password);

  return user;
}
```

## Interface

### PasswordGeneratorOptions

```typescript
interface PasswordGeneratorOptions {
  length?: number;                // Comprimento da senha (4-128, padrão: 12)
  includeUppercase?: boolean;     // Incluir A-Z (padrão: true)
  includeLowercase?: boolean;     // Incluir a-z (padrão: true)
  includeNumbers?: boolean;       // Incluir 0-9 (padrão: true)
  includeSpecialChars?: boolean;  // Incluir !@#$%^&*()... (padrão: true)
  excludeSimilar?: boolean;       // Excluir 0O1lI5S2Z (padrão: true)
}
```

### PasswordGeneratorResult

```typescript
interface PasswordGeneratorResult {
  password: string;                    // A senha gerada
  strength: 'weak' | 'medium' | 'strong'; // Força da senha
  entropy: number;                     // Entropia em bits
}
```

### UsePasswordGeneratorReturn

```typescript
interface UsePasswordGeneratorReturn {
  generatePassword: (options?: PasswordGeneratorOptions) => PasswordGeneratorResult;
  isGenerating: boolean;               // Estado de geração
  lastGenerated: PasswordGeneratorResult | null; // Último resultado
}
```

## Configuração Padrão

```typescript
const DEFAULT_OPTIONS = {
  length: 12,
  includeUppercase: true,
  includeLowercase: true,
  includeNumbers: true,
  includeSpecialChars: true,
  excludeSimilar: true
};
```

## Critérios de Segurança

As senhas geradas automaticamente atendem aos seguintes critérios:

- **Comprimento mínimo**: 8 caracteres (configurável)
- **Complexidade**: Pelo menos um caractere de cada tipo selecionado
- **Aleatoriedade**: Usa `crypto.getRandomValues()` para segurança criptográfica
- **Unicidade**: Cada geração produz uma senha única
- **Entropia**: Calculada baseada no conjunto de caracteres e comprimento

## Força da Senha

A força é determinada pela entropia:

- **Weak** (Fraca): < 40 bits de entropia
- **Medium** (Média): 40-60 bits de entropia  
- **Strong** (Forte): > 60 bits de entropia

## Caracteres Similares

Quando `excludeSimilar: true`, os seguintes caracteres são excluídos para evitar confusão:

- `0` (zero) vs `O` (letra O)
- `1` (um) vs `l` (letra L minúscula) vs `I` (letra i maiúscula)
- `5` (cinco) vs `S` (letra S)
- `2` (dois) vs `Z` (letra Z)

## Tratamento de Erros

O hook implementa tratamento robusto de erros:

```typescript
try {
  const result = generatePassword({ length: 16 });
  // Usar resultado
} catch (error) {
  console.error('Erro ao gerar senha:', error);
  // Implementar fallback
}
```

Erros comuns:
- Comprimento inválido (< 4 ou > 128)
- Nenhum conjunto de caracteres selecionado
- Falha após múltiplas tentativas de geração

## Segurança

### Boas Práticas

1. **Sempre limpe dados sensíveis**:
```typescript
import { clearSensitiveData } from '@/hooks';

const password = generatePasswordSync().password;
// ... usar senha
clearSensitiveData(password); // Limpar da memória
```

2. **Use HTTPS para transmissão**:
```typescript
// ✅ Correto - sempre usar HTTPS
const response = await fetch('https://api.example.com/users', {
  method: 'POST',
  body: JSON.stringify({ password })
});

// ❌ Incorreto - nunca usar HTTP para senhas
```

3. **Não logue senhas**:
```typescript
// ❌ Incorreto
console.log('Senha gerada:', password);

// ✅ Correto
console.log('Senha gerada com sucesso. Força:', result.strength);
```

### Considerações de Performance

- Geração é rápida (< 1ms para senhas normais)
- Cache interno de conjuntos de caracteres
- Timeout automático para evitar loops infinitos
- Limpeza automática de variáveis temporárias

## Exemplos Avançados

### Geração em Lote

```typescript
function generateMultiplePasswords(count: number) {
  const { generatePassword } = usePasswordGenerator();
  const passwords = [];

  for (let i = 0; i < count; i++) {
    passwords.push(generatePassword());
  }

  return passwords;
}
```

### Validação Personalizada

```typescript
function generatePasswordWithCustomValidation() {
  const { generatePassword } = usePasswordGenerator();
  
  let result;
  let attempts = 0;
  const maxAttempts = 10;

  do {
    result = generatePassword();
    attempts++;
    
    // Validação personalizada: deve conter pelo menos 2 números
    const numberCount = (result.password.match(/\d/g) || []).length;
    
    if (numberCount >= 2) break;
    
  } while (attempts < maxAttempts);

  return result;
}
```

### Integração com Formulários

```typescript
import { usePasswordGenerator } from '@/hooks';
import { useState } from 'react';

function PasswordForm() {
  const [password, setPassword] = useState('');
  const { generatePassword, isGenerating } = usePasswordGenerator();

  const handleGenerate = () => {
    const result = generatePassword({
      length: 14,
      excludeSimilar: true
    });
    setPassword(result.password);
  };

  return (
    <form>
      <div>
        <label>Senha:</label>
        <input 
          type="password" 
          value={password}
          onChange={(e) => setPassword(e.target.value)}
        />
        <button 
          type="button" 
          onClick={handleGenerate}
          disabled={isGenerating}
        >
          Gerar Senha Segura
        </button>
      </div>
    </form>
  );
}
```

## Troubleshooting

### Problema: Senha não atende aos critérios

**Solução**: Verifique se pelo menos um tipo de caractere está habilitado:

```typescript
// ❌ Problemático - nenhum tipo habilitado
const result = generatePassword({
  includeUppercase: false,
  includeLowercase: false,
  includeNumbers: false,
  includeSpecialChars: false
});

// ✅ Correto - pelo menos um tipo habilitado
const result = generatePassword({
  includeLowercase: true,
  includeNumbers: true
});
```

### Problema: Erro "Não foi possível gerar senha"

**Solução**: Reduza os critérios ou aumente o comprimento:

```typescript
// Se falhar com configuração complexa
try {
  return generatePassword({ length: 8, /* critérios complexos */ });
} catch (error) {
  // Fallback com critérios mais simples
  return generatePassword({ 
    length: 10,
    includeUppercase: true,
    includeLowercase: true,
    includeNumbers: true,
    includeSpecialChars: false
  });
}
```

### Problema: Performance lenta

**Solução**: Evite gerações desnecessárias e use debounce:

```typescript
import { useMemo, useCallback } from 'react';
import { debounce } from 'lodash';

function OptimizedPasswordGenerator() {
  const { generatePassword } = usePasswordGenerator();
  
  const debouncedGenerate = useCallback(
    debounce(() => {
      const result = generatePassword();
      // Processar resultado
    }, 300),
    [generatePassword]
  );

  return (
    <button onClick={debouncedGenerate}>
      Gerar Senha
    </button>
  );
}
```