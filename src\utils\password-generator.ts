/**
 * Utilitário para geração de senhas aleatórias seguras (sem hooks React)
 * Utiliza crypto.getRandomValues() para aleatoriedade criptograficamente segura
 */

export interface PasswordGeneratorOptions {
  length?: number;
  includeUppercase?: boolean;
  includeLowercase?: boolean;
  includeNumbers?: boolean;
  includeSpecialChars?: boolean;
  excludeSimilar?: boolean;
}

export interface PasswordGeneratorResult {
  password: string;
  strength: 'weak' | 'medium' | 'strong';
  entropy: number;
}

// Configuração padrão para senhas seguras
const DEFAULT_OPTIONS: Required<PasswordGeneratorOptions> = {
  length: 12,
  includeUppercase: true,
  includeLowercase: true,
  includeNumbers: true,
  includeSpecialChars: true,
  excludeSimilar: true
};

// Conjuntos de caracteres
const CHARACTER_SETS = {
  uppercase: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
  lowercase: 'abcdefghijklmnopqrstuvwxyz',
  numbers: '0123456789',
  specialChars: '!@#$%^&*()_+-=[]{}|;:,.<>?',
  similarChars: '0O1lI5S2Z' // Caracteres similares a serem excluídos
} as const;

/**
 * Calcula a entropia de uma senha baseada no conjunto de caracteres e comprimento
 */
function calculateEntropy(password: string, charsetSize: number): number {
  return Math.log2(Math.pow(charsetSize, password.length));
}

/**
 * Determina a força da senha baseada na entropia
 */
function determineStrength(entropy: number): 'weak' | 'medium' | 'strong' {
  if (entropy < 40) return 'weak';
  if (entropy < 60) return 'medium';
  return 'strong';
}

// Cache para regex compiladas
const regexCache = new Map<string, RegExp>();

/**
 * Obtém regex compilada com cache
 */
function getCachedRegex(pattern: string): RegExp {
  if (!regexCache.has(pattern)) {
    regexCache.set(pattern, new RegExp(pattern));
  }
  return regexCache.get(pattern)!;
}

/**
 * Valida se a senha atende aos critérios mínimos de segurança (otimizado)
 */
function validatePasswordCriteria(password: string, options: Required<PasswordGeneratorOptions>): boolean {
  // Verificar comprimento mínimo
  if (password.length < 8) return false;

  // Otimização: verificar apenas critérios necessários
  const checks: Array<() => boolean> = [];

  if (options.includeUppercase) {
    checks.push(() => getCachedRegex('[A-Z]').test(password));
  }
  if (options.includeLowercase) {
    checks.push(() => getCachedRegex('[a-z]').test(password));
  }
  if (options.includeNumbers) {
    checks.push(() => getCachedRegex('[0-9]').test(password));
  }
  if (options.includeSpecialChars) {
    checks.push(() => getCachedRegex('[!@#$%^&*()_+\\-=\\[\\]{}|;:,.<>?]').test(password));
  }

  // Executar todas as verificações (early return se alguma falhar)
  return checks.every(check => check());
}

// Cache para conjuntos de caracteres otimizados
const charsetCache = new Map<string, string>();

/**
 * Constrói conjunto de caracteres otimizado com cache
 */
function buildCharset(options: Required<PasswordGeneratorOptions>): string {
  // Criar chave única para cache
  const cacheKey = `${options.includeUppercase}-${options.includeLowercase}-${options.includeNumbers}-${options.includeSpecialChars}-${options.excludeSimilar}`;
  
  // Verificar cache primeiro
  if (charsetCache.has(cacheKey)) {
    return charsetCache.get(cacheKey)!;
  }

  // Construir conjunto de caracteres
  let charset = '';
  
  if (options.includeUppercase) {
    charset += CHARACTER_SETS.uppercase;
  }
  if (options.includeLowercase) {
    charset += CHARACTER_SETS.lowercase;
  }
  if (options.includeNumbers) {
    charset += CHARACTER_SETS.numbers;
  }
  if (options.includeSpecialChars) {
    charset += CHARACTER_SETS.specialChars;
  }

  // Remover caracteres similares se solicitado (otimizado)
  if (options.excludeSimilar) {
    const similarCharsSet = new Set(CHARACTER_SETS.similarChars);
    charset = charset.split('').filter(char => !similarCharsSet.has(char)).join('');
  }

  if (charset.length === 0) {
    throw new Error('Nenhum conjunto de caracteres selecionado para geração de senha');
  }

  // Armazenar no cache
  charsetCache.set(cacheKey, charset);
  
  return charset;
}

/**
 * Gera uma senha aleatória segura usando crypto.getRandomValues() (otimizado)
 */
function generateSecurePassword(options: Required<PasswordGeneratorOptions>): string {
  const charset = buildCharset(options);
  const charsetLength = charset.length;
  
  // Otimização: usar array tipado menor se possível
  const arrayType = charsetLength <= 256 ? Uint8Array : Uint32Array;
  const array = new arrayType(options.length);
  crypto.getRandomValues(array);
  
  // Otimização: usar array de caracteres para melhor performance
  const result = new Array(options.length);
  for (let i = 0; i < options.length; i++) {
    result[i] = charset[array[i] % charsetLength];
  }

  return result.join('');
}

/**
 * Função para gerar senha sem hook (para uso em server actions)
 */
export function generatePasswordSync(options: PasswordGeneratorOptions = {}): PasswordGeneratorResult {
  const finalOptions: Required<PasswordGeneratorOptions> = {
    ...DEFAULT_OPTIONS,
    ...options
  };

  // Validar opções
  if (finalOptions.length < 4) {
    throw new Error('Comprimento mínimo da senha deve ser 4 caracteres');
  }

  if (finalOptions.length > 128) {
    throw new Error('Comprimento máximo da senha deve ser 128 caracteres');
  }

  let password = '';
  let attempts = 0;
  const maxAttempts = 10;

  // Tentar gerar senha que atenda aos critérios
  do {
    if (attempts >= maxAttempts) {
      throw new Error('Não foi possível gerar senha que atenda aos critérios após múltiplas tentativas');
    }

    password = generateSecurePassword(finalOptions);
    attempts++;
  } while (!validatePasswordCriteria(password, finalOptions));

  // Calcular entropia (otimizado - usar charset real)
  const charset = buildCharset(finalOptions);
  const entropy = calculateEntropy(password, charset.length);
  const strength = determineStrength(entropy);

  return {
    password,
    strength,
    entropy: Math.round(entropy * 100) / 100
  };
}

/**
 * Função otimizada para limpar dados sensíveis da memória
 */
export function clearSensitiveData(data: string): void {
  if (typeof data !== 'string' || data.length === 0) return;

  try {
    // Sobrescrever com zeros (mais eficiente)
    const length = data.length;
    const zeros = '\0'.repeat(length);
    
    // Tentar sobrescrever a string original (limitado em JS)
    // Isso é mais simbólico, pois strings são imutáveis em JS
    data = zeros;
    
    // Forçar garbage collection se disponível
    if (typeof global !== 'undefined' && global.gc) {
      global.gc();
    } else if (typeof window !== 'undefined' && (window as any).gc) {
      (window as any).gc();
    }
  } catch (error) {
    // Falha silenciosa - limpeza de memória é best effort
    console.debug('Aviso: não foi possível limpar dados sensíveis:', error);
  }
}

/**
 * Função para limpar múltiplas variáveis sensíveis
 */
export function clearMultipleSensitiveData(...dataArray: string[]): void {
  dataArray.forEach(data => {
    if (data) clearSensitiveData(data);
  });
}