'use server';

import { z } from 'zod';
import { createTenantServerClient } from '@/services/supabase/server';
import { getTenantSlug } from '@/services/tenant/';

export interface StudentActivePlan {
  id: string;
  title: string;
  plan_type: string;
  status: 'active' | 'canceled' | 'paused' | 'expired';
  start_date: string;
  end_date?: string;
  next_billing_date?: string;
  pricing_config?: any;
  duration_config?: any;
  access_config?: any;
  benefits?: string[];
}

export interface StudentActivePlansData {
  student_id: string;
  active_plans: StudentActivePlan[];
  has_active_membership: boolean;
  primary_plan?: StudentActivePlan;
}

interface RequestData {
  studentId: string;
}

const schema = z.object({
  studentId: z.string().uuid('ID do aluno inválido'),
});

/**
 * Busca os planos ativos de um aluno específico
 * Retorna todas as memberships ativas e seus respectivos planos
 */
export async function getStudentActivePlans(
  payload: unknown,
): Promise<
  | { success: true; data: StudentActivePlansData }
  | { success: false; error: string }
> {
  const parsed = schema.safeParse(payload);
  if (!parsed.success) {
    return { success: false, error: 'Parâmetros inválidos' };
  }

  const { studentId } = parsed.data as RequestData;

  try {
    console.log('Iniciando busca de planos ativos do aluno:', studentId);
    
    const supabase = await createTenantServerClient();
    const tenantSlug = await getTenantSlug();

    if (!tenantSlug) {
      return { success: false, error: 'Tenant não encontrado' };
    }

    // Buscar tenant ID
    const { data: tenant, error: tenantError } = await supabase
      .from('tenants')
      .select('id')
      .eq('slug', tenantSlug)
      .single();

    if (tenantError || !tenant) {
      return { success: false, error: 'Erro ao buscar configurações do tenant' };
    }

    // Buscar memberships ativas do aluno com seus planos
    const { data: memberships, error: membershipsError } = await supabase
      .from('memberships')
      .select(`
        id,
        status,
        start_date,
        end_date,
        next_billing_date,
        plans!inner(
          id,
          title,
          plan_type,
          status,
          pricing_config,
          duration_config,
          access_config,
          benefits
        )
      `)
      .eq('tenant_id', tenant.id)
      .eq('student_id', studentId)
      .eq('status', 'active')
      .order('created_at', { ascending: false });

    if (membershipsError) {
      console.error('Erro ao buscar memberships:', membershipsError);
      return { success: false, error: 'Erro ao buscar planos do aluno' };
    }

    // Transformar dados para o formato esperado
    const activePlans: StudentActivePlan[] = (memberships || []).map((membership: any) => ({
      id: membership.plans.id,
      title: membership.plans.title,
      plan_type: membership.plans.plan_type,
      status: membership.status,
      start_date: membership.start_date,
      end_date: membership.end_date,
      next_billing_date: membership.next_billing_date,
      pricing_config: membership.plans.pricing_config,
      duration_config: membership.plans.duration_config,
      access_config: membership.plans.access_config,
      benefits: membership.plans.benefits || [],
    }));

    // Determinar plano primário (primeiro da lista ou o mais recente)
    const primaryPlan = activePlans.length > 0 ? activePlans[0] : undefined;

    const result: StudentActivePlansData = {
      student_id: studentId,
      active_plans: activePlans,
      has_active_membership: activePlans.length > 0,
      primary_plan: primaryPlan,
    };

    console.log(`Encontrados ${activePlans.length} planos ativos para o aluno`);
    return { success: true, data: result };

  } catch (err) {
    console.error('Erro inesperado ao buscar planos ativos:', err);
    return { success: false, error: 'Erro interno do servidor' };
  }
}

/**
 * Busca informações resumidas de membership para check-in
 * Compatível com a interface existente do sistema de check-in
 */
export async function getStudentMembershipForCheckIn(
  studentId: string,
  tenantId: string,
  supabase: any
): Promise<{
  type?: string;
  status?: 'active' | 'canceled' | 'past_due' | 'unpaid' | string;
  expiresAt?: string;
} | null> {
  try {
    // Buscar membership ativa mais recente
    const { data: membership, error } = await supabase
      .from('memberships')
      .select(`
        status,
        end_date,
        next_billing_date,
        plans!inner(
          title,
          plan_type,
          duration_config
        )
      `)
      .eq('tenant_id', tenantId)
      .eq('student_id', studentId)
      .eq('status', 'active')
      .order('created_at', { ascending: false })
      .limit(1)
      .maybeSingle();

    if (error || !membership) {
      return null;
    }

    // Verificar se o plano é contínuo (ongoing) ou tem data de expiração
    const durationType = membership.plans.duration_config?.type;
    let expiresAt: string | undefined;

    if (durationType === 'ongoing') {
      // Planos contínuos não têm data de expiração
      expiresAt = undefined;
    } else {
      // Planos com duração definida usam end_date ou next_billing_date
      expiresAt = membership.end_date || membership.next_billing_date;
    }

    return {
      type: membership.plans.title || 'Plano ativo',
      status: membership.status,
      expiresAt: expiresAt,
    };

  } catch (err) {
    console.error('Erro ao buscar membership para check-in:', err);
    return null;
  }
}

/**
 * Busca planos ativos por modalidade para um aluno específico
 * Útil para verificar se o aluno tem acesso a uma modalidade específica
 */
export async function getStudentActivePlansByModality(
  studentId: string,
  modalityId: string
): Promise<
  | { success: true; data: StudentActivePlan[] }
  | { success: false; error: string }
> {
  try {
    const supabase = await createTenantServerClient();
    const tenantSlug = await getTenantSlug();

    if (!tenantSlug) {
      return { success: false, error: 'Tenant não encontrado' };
    }

    // Buscar tenant ID
    const { data: tenant, error: tenantError } = await supabase
      .from('tenants')
      .select('id')
      .eq('slug', tenantSlug)
      .single();

    if (tenantError || !tenant) {
      return { success: false, error: 'Erro ao buscar configurações do tenant' };
    }

    // Buscar memberships ativas do aluno com planos que incluem a modalidade
    const { data: memberships, error: membershipsError } = await supabase
      .from('memberships')
      .select(`
        id,
        status,
        start_date,
        end_date,
        next_billing_date,
        plans!inner(
          id,
          title,
          plan_type,
          status,
          pricing_config,
          duration_config,
          access_config,
          benefits
        )
      `)
      .eq('tenant_id', tenant.id)
      .eq('student_id', studentId)
      .eq('status', 'active');

    if (membershipsError) {
      return { success: false, error: 'Erro ao buscar planos do aluno' };
    }

    // Filtrar planos que incluem a modalidade especificada
    const filteredMemberships = (memberships || []).filter((membership: any) => {
      const accessConfig = membership.plans.access_config;
      if (!accessConfig || !accessConfig.modalities) {
        return false;
      }
      return accessConfig.modalities.includes(modalityId);
    });

    // Transformar dados para o formato esperado
    const activePlans: StudentActivePlan[] = filteredMemberships.map((membership: any) => ({
      id: membership.plans.id,
      title: membership.plans.title,
      plan_type: membership.plans.plan_type,
      status: membership.status,
      start_date: membership.start_date,
      end_date: membership.end_date,
      next_billing_date: membership.next_billing_date,
      pricing_config: membership.plans.pricing_config,
      duration_config: membership.plans.duration_config,
      access_config: membership.plans.access_config,
      benefits: membership.plans.benefits || [],
    }));

    return { success: true, data: activePlans };

  } catch (err) {
    console.error('Erro ao buscar planos ativos por modalidade:', err);
    return { success: false, error: 'Erro interno do servidor' };
  }
}
