/**
 * Tipos e interfaces para o sistema de rastreamento de histórico de status de usuários
 * 
 * Este arquivo contém todas as definições de tipos relacionadas ao sistema de histórico
 * de status de usuários, incluindo interfaces para registros de histórico, operações
 * de mudança de status e consultas de métricas baseadas em histórico.
 */

/**
 * Status possíveis para um usuário no sistema
 */
export type UserStatus = 'active' | 'inactive' | 'suspended'

/**
 * Interface principal para registros de histórico de status de usuários
 * 
 * Representa um registro individual de mudança de status na tabela user_status_history.
 * Cada registro captura uma mudança de status específica com contexto completo.
 * 
 * @interface UserStatusHistory
 */
export interface UserStatusHistory {
  /** Identificador único do registro de histórico */
  id: string
  
  /** ID do tenant para isolamento multi-tenant */
  tenant_id: string
  
  /** ID da filial para suporte multi-branch */
  branch_id: string
  
  /** ID do usuário que teve o status alterado */
  user_id: string
  
  /** Status anterior do usuário (null para registros iniciais) */
  old_status: UserStatus | null
  
  /** Novo status do usuário após a mudança */
  new_status: UserStatus
  
  /** Timestamp preciso de quando a mudança ocorreu */
  changed_at: string
  
  /** ID do usuário que executou a mudança (opcional) */
  changed_by?: string
  
  /** Motivo ou observação sobre a mudança (opcional) */
  reason?: string
  
  /** Metadados adicionais em formato JSON */
  metadata: Record<string, any>
  
  /** Timestamp de criação do registro */
  created_at: string
}

/**
 * Interface para parâmetros de mudança de status
 * 
 * Utilizada quando se deseja alterar o status de um usuário de forma controlada,
 * garantindo que o contexto da mudança seja registrado adequadamente.
 * 
 * @interface StatusChangeParams
 */
export interface StatusChangeParams {
  /** ID do usuário que terá o status alterado */
  user_id: string
  
  /** Novo status a ser aplicado */
  new_status: UserStatus
  
  /** ID do usuário que está executando a mudança */
  changed_by?: string
  
  /** Motivo da mudança de status */
  reason?: string
  
  /** Metadados adicionais sobre a mudança */
  metadata?: Record<string, any>
}

/**
 * Interface para consultas de histórico de status
 * 
 * Define os parâmetros disponíveis para filtrar e consultar o histórico
 * de status de usuários, permitindo análises específicas por período,
 * tenant, filial ou usuário.
 * 
 * @interface StatusHistoryQuery
 */
export interface StatusHistoryQuery {
  /** ID do tenant (obrigatório para isolamento) */
  tenant_id: string
  
  /** ID da filial (opcional, para filtrar por filial específica) */
  branch_id?: string
  
  /** ID do usuário específico (opcional, para histórico individual) */
  user_id?: string
  
  /** Data de início do período de consulta */
  start_date?: string
  
  /** Data de fim do período de consulta */
  end_date?: string
  
  /** Status específico para filtrar */
  status?: UserStatus
  
  /** Limite de registros retornados */
  limit?: number
  
  /** Offset para paginação */
  offset?: number
}

/**
 * Interface para resposta de consultas de histórico
 * 
 * Estrutura padrão retornada pelas funções de consulta de histórico,
 * incluindo os dados solicitados e informações de paginação.
 * 
 * @interface StatusHistoryResponse
 */
export interface StatusHistoryResponse {
  /** Lista de registros de histórico */
  data: UserStatusHistory[]
  
  /** Número total de registros disponíveis */
  total_count: number
  
  /** Indica se há mais páginas disponíveis */
  has_more: boolean
  
  /** Próximo offset para paginação */
  next_offset?: number
}

/**
 * Interface para métricas baseadas em histórico
 * 
 * Representa dados agregados calculados a partir do histórico de status,
 * fornecendo insights sobre a evolução dos usuários ao longo do tempo.
 * 
 * @interface HistoryBasedMetrics
 */
export interface HistoryBasedMetrics {
  /** Número de usuários ativos no momento atual */
  active_users_now: number
  
  /** Número de usuários ativos no final do mês anterior */
  active_users_last_month: number
  
  /** Número de novos usuários ativos no mês atual */
  new_users_now: number
  
  /** Número de novos usuários ativos no mês anterior */
  new_users_last_month: number
  
  /** Número de usuários suspensos atualmente */
  suspended_users_now?: number
  
  /** Data de referência para os cálculos */
  reference_date: string
}

/**
 * Interface para dados de gráfico com histórico
 * 
 * Estrutura de dados para gráficos que mostram a evolução diária
 * dos status de usuários, baseada no histórico real de mudanças.
 * 
 * @interface HistoryChartData
 */
export interface HistoryChartData {
  /** Data no formato de string legível */
  date: string
  
  /** Número de usuários ativos nesta data */
  active_count: number
  
  /** Número de usuários inativos nesta data */
  inactive_count: number
  
  /** Número de usuários suspensos nesta data */
  suspended_count: number
}

/**
 * Interface para utilitários de consulta de status
 * 
 * Define métodos auxiliares para consultar o status de usuários
 * em datas específicas, facilitando análises históricas.
 * 
 * @interface StatusQueryUtils
 */
export interface StatusQueryUtils {
  /** Obter status de um usuário em uma data específica */
  getUserStatusAtDate: (user_id: string, date: string) => Promise<UserStatus | null>
  
  /** Obter histórico completo de um usuário */
  getUserStatusHistory: (user_id: string) => Promise<UserStatusHistory[]>
  
  /** Verificar se uma mudança de status é válida */
  isValidStatusChange: (from: UserStatus | null, to: UserStatus) => boolean
  
  /** Obter último registro de status de um usuário */
  getLastStatusChange: (user_id: string) => Promise<UserStatusHistory | null>
}

/**
 * Tipo para funções de callback de mudança de status
 * 
 * Define a assinatura para funções que são executadas quando
 * uma mudança de status é detectada ou processada.
 */
export type StatusChangeCallback = (
  change: UserStatusHistory,
  context?: Record<string, any>
) => void | Promise<void>

/**
 * Interface para configuração do sistema de histórico
 * 
 * Permite configurar comportamentos específicos do sistema
 * de rastreamento de histórico de status.
 * 
 * @interface StatusHistoryConfig
 */
export interface StatusHistoryConfig {
  /** Habilitar logging detalhado de mudanças */
  enable_detailed_logging?: boolean
  
  /** Callback executado após cada mudança de status */
  on_status_change?: StatusChangeCallback
  
  /** Metadados padrão adicionados a cada mudança */
  default_metadata?: Record<string, any>
  
  /** Validações customizadas para mudanças de status */
  custom_validations?: Array<(from: UserStatus | null, to: UserStatus) => boolean>
}

/**
 * Tipos utilitários para operações de status
 */

/**
 * Tipo para representar transições válidas de status
 * 
 * Define quais mudanças de status são permitidas no sistema,
 * ajudando a validar operações antes de serem executadas.
 */
export type StatusTransition = {
  from: UserStatus | null
  to: UserStatus
  allowed: boolean
  reason?: string
}

/**
 * Mapa de transições válidas de status
 * 
 * Define as regras de negócio para mudanças de status,
 * especificando quais transições são permitidas.
 */
export const VALID_STATUS_TRANSITIONS: Record<string, StatusTransition[]> = {
  // Transições a partir de status null (usuário novo)
  'null': [
    { from: null, to: 'active', allowed: true, reason: 'Ativação inicial do usuário' },
    { from: null, to: 'inactive', allowed: true, reason: 'Criação como usuário inativo' },
    { from: null, to: 'suspended', allowed: false, reason: 'Usuários novos não podem ser criados como suspensos' }
  ],
  
  // Transições a partir de status ativo
  'active': [
    { from: 'active', to: 'inactive', allowed: true, reason: 'Desativação de usuário ativo' },
    { from: 'active', to: 'suspended', allowed: true, reason: 'Suspensão de usuário ativo' },
    { from: 'active', to: 'active', allowed: false, reason: 'Usuário já está ativo' }
  ],
  
  // Transições a partir de status inativo
  'inactive': [
    { from: 'inactive', to: 'active', allowed: true, reason: 'Reativação de usuário inativo' },
    { from: 'inactive', to: 'suspended', allowed: true, reason: 'Suspensão de usuário inativo' },
    { from: 'inactive', to: 'inactive', allowed: false, reason: 'Usuário já está inativo' }
  ],
  
  // Transições a partir de status suspenso
  'suspended': [
    { from: 'suspended', to: 'active', allowed: true, reason: 'Reativação de usuário suspenso' },
    { from: 'suspended', to: 'inactive', allowed: true, reason: 'Inativação de usuário suspenso' },
    { from: 'suspended', to: 'suspended', allowed: false, reason: 'Usuário já está suspenso' }
  ]
}

/**
 * Interface para resultado de validação de mudança de status
 * 
 * Retornada por funções de validação para indicar se uma mudança
 * de status é válida e fornecer contexto sobre a decisão.
 */
export interface StatusChangeValidation {
  /** Indica se a mudança é válida */
  is_valid: boolean
  
  /** Mensagem explicativa sobre a validação */
  message: string
  
  /** Código de erro (se aplicável) */
  error_code?: string
  
  /** Dados adicionais sobre a validação */
  metadata?: Record<string, any>
}

/**
 * Interface para estatísticas de status por período
 * 
 * Fornece uma visão agregada dos status de usuários
 * em um período específico, útil para relatórios e análises.
 */
export interface StatusPeriodStats {
  /** Período de referência */
  period: {
    start_date: string
    end_date: string
  }
  
  /** Estatísticas por status */
  stats: {
    active: {
      count: number
      percentage: number
    }
    inactive: {
      count: number
      percentage: number
    }
    suspended: {
      count: number
      percentage: number
    }
  }
  
  /** Total de usuários no período */
  total_users: number
  
  /** Número de mudanças de status no período */
  status_changes: number
}

/**
 * Interface para análise de tendências de status
 * 
 * Fornece insights sobre como os status dos usuários
 * estão evoluindo ao longo do tempo.
 */
export interface StatusTrendAnalysis {
  /** Período analisado */
  period: {
    start_date: string
    end_date: string
  }
  
  /** Tendências por status */
  trends: {
    active: {
      current_count: number
      previous_count: number
      change_percentage: number
      trend: 'up' | 'down' | 'stable'
    }
    inactive: {
      current_count: number
      previous_count: number
      change_percentage: number
      trend: 'up' | 'down' | 'stable'
    }
    suspended: {
      current_count: number
      previous_count: number
      change_percentage: number
      trend: 'up' | 'down' | 'stable'
    }
  }
  
  /** Análise geral */
  overall_trend: 'improving' | 'declining' | 'stable'
  
  /** Recomendações baseadas na análise */
  recommendations?: string[]
}

/**
 * Tipo para filtros avançados de consulta de histórico
 * 
 * Permite consultas mais específicas e complexas no histórico
 * de status, incluindo filtros por múltiplos critérios.
 */
export type AdvancedHistoryFilter = {
  /** Filtrar por múltiplos usuários */
  user_ids?: string[]
  
  /** Filtrar por múltiplas filiais */
  branch_ids?: string[]
  
  /** Filtrar por tipo de mudança */
  change_types?: Array<'activation' | 'deactivation' | 'suspension' | 'reactivation'>
  
  /** Filtrar por usuário que fez a mudança */
  changed_by_ids?: string[]
  
  /** Filtrar por presença de motivo */
  has_reason?: boolean
  
  /** Filtrar por metadados específicos */
  metadata_filters?: Record<string, any>
  
  /** Ordenação dos resultados */
  order_by?: 'changed_at' | 'user_id' | 'new_status'
  
  /** Direção da ordenação */
  order_direction?: 'asc' | 'desc'
}

/**
 * Interface para exportação de dados de histórico
 * 
 * Define a estrutura para exportar dados de histórico
 * em diferentes formatos para análise externa.
 */
export interface HistoryExportData {
  /** Metadados da exportação */
  export_info: {
    generated_at: string
    tenant_id: string
    branch_id?: string
    period: {
      start_date: string
      end_date: string
    }
    total_records: number
  }
  
  /** Dados do histórico */
  history_records: UserStatusHistory[]
  
  /** Estatísticas resumidas */
  summary_stats: StatusPeriodStats
  
  /** Formato da exportação */
  format: 'json' | 'csv' | 'xlsx'
}