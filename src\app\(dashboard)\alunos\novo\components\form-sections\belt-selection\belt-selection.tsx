'use client';

import { useFormContext } from "react-hook-form";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Card, CardContent } from "@/components/ui/card";
import { NovoAlunoFormValues } from "../../../actions/schemas/aluno-schema";
import { useEffect, useMemo } from "react";

import { cn } from "@/lib/utils";
import { Award, Loader2, AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue
} from "@/components/ui/select";
import { JiuJitsuBelt } from "@/src/components/belt";
import { GraduationLevel } from "@/services/belts/levels";
import { useModalities } from "@/hooks/graduation/use-modalities";
import { useBeltLevels } from "@/hooks/graduation/use-belt-levels";

export default function BeltSelectionSection() {
    const { control, watch, setValue } = useFormContext<NovoAlunoFormValues>();
    const selectedModalityId = watch('selected_modality_id') || '';
    const selectedBeltLevelId = watch('selected_belt_level_id') || '';

    // Usar hooks customizados com React Query
    const {
        data: modalities = [],
        isLoading: isLoadingModalities,
        error: modalitiesError,
        refetch: refetchModalities
    } = useModalities();

    // Encontrar a modalidade selecionada para obter o slug
    const selectedModality = useMemo(() => {
        return modalities.find(m => m.id === selectedModalityId);
    }, [modalities, selectedModalityId]);

    const {
        data: beltLevels = [],
        isLoading: isLoadingBelts,
        error: beltLevelsError,
        refetch: refetchBeltLevels
    } = useBeltLevels(selectedModality?.slug || null);

    // Auto-selecionar a primeira faixa quando os níveis são carregados
    useEffect(() => {
        if (beltLevels.length > 0 && !selectedBeltLevelId && selectedModalityId) {
            setValue('selected_belt_level_id', beltLevels[0].id);
        }
    }, [beltLevels, selectedBeltLevelId, selectedModalityId, setValue]);

    // Limpar seleção de faixa quando modalidade muda
    useEffect(() => {
        if (!selectedModalityId) {
            setValue('selected_belt_level_id', '');
        }
    }, [selectedModalityId, setValue]);

    // Determinar se há erro
    const error = modalitiesError?.message || beltLevelsError?.message;

    // Função para renderizar a faixa visual
    const renderBeltVisual = (belt: GraduationLevel, size: 'sm' | 'xs' = 'sm') => {
        const sizeClasses = {
            xs: "w-12 h-3",
            sm: "w-16 h-4"
        };

        return (
            <div className="flex items-center gap-2">
                <JiuJitsuBelt
                    beltColor={belt.belt_color}
                    degrees={belt.degree}
                    stripeColor={belt.stripe_color || undefined}
                    showCenterLine={belt.show_center_line || false}
                    centerLineColor={belt.center_line_color || undefined}
                    className={sizeClasses[size]}
                />
                <span className={cn(
                    "font-medium text-slate-700 dark:text-slate-300",
                    size === 'xs' ? "text-xs" : "text-sm"
                )}>
                    {belt.label}
                </span>
            </div>
        );
    };



    if (isLoadingModalities) {
        return (
            <Card className="overflow-hidden border-slate-200 dark:border-slate-700 shadow-sm bg-white dark:bg-slate-800">
                <div className="bg-slate-50 dark:bg-slate-700/50 px-6 py-4 border-b border-slate-200 dark:border-slate-700">
                    <h2 className="text-lg font-semibold text-slate-900 dark:text-slate-100 flex items-center">
                        <Award className="w-5 h-5 mr-2 text-slate-500 dark:text-slate-400" />
                        Faixa Inicial
                    </h2>
                </div>
                <CardContent className="p-6">
                    <div className="flex items-center justify-center py-8">
                        <Loader2 className="w-6 h-6 animate-spin text-slate-400" />
                        <span className="ml-2 text-slate-600 dark:text-slate-400">Carregando modalidades...</span>
                    </div>
                </CardContent>
            </Card>
        );
    }

    // Função para tentar novamente em caso de erro
    const handleRetry = () => {
        if (modalitiesError) {
            refetchModalities();
        }
        if (beltLevelsError) {
            refetchBeltLevels();
        }
    };

    if (error) {
        return (
            <Card className="overflow-hidden border-slate-200 dark:border-slate-700 shadow-sm bg-white dark:bg-slate-800">
                <div className="bg-slate-50 dark:bg-slate-700/50 px-6 py-4 border-b border-slate-200 dark:border-slate-700">
                    <h2 className="text-lg font-semibold text-slate-900 dark:text-slate-100 flex items-center">
                        <Award className="w-5 h-5 mr-2 text-slate-500 dark:text-slate-400" />
                        Faixa Inicial
                    </h2>
                </div>
                <CardContent className="p-6">
                    <div className="text-center py-8">
                        <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
                        <p className="text-red-600 dark:text-red-400 mb-4">{error}</p>
                        <Button
                            variant="outline"
                            onClick={handleRetry}
                            className="mt-4"
                        >
                            Tentar novamente
                        </Button>
                    </div>
                </CardContent>
            </Card>
        );
    }

    return (
        <Card className="overflow-hidden border-slate-200 dark:border-slate-700 shadow-sm hover:shadow transition-all duration-200 bg-white dark:bg-slate-800">
            <div className="bg-slate-50 dark:bg-slate-700/50 px-6 py-4 border-b border-slate-200 dark:border-slate-700">
                <h2 className="text-lg font-semibold text-slate-900 dark:text-slate-100 flex items-center">
                    <Award className="w-5 h-5 mr-2 text-slate-500 dark:text-slate-400" />
                    Faixa Inicial
                </h2>
                <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
                    Selecione a modalidade e a faixa inicial do aluno
                </p>
            </div>

            <CardContent className="p-6 space-y-6">
                {/* Seleção de Modalidade */}
                <FormField
                    control={control}
                    name="selected_modality_id"
                    render={({ field }) => (
                        <FormItem>
                            <FormLabel className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                Modalidade*
                            </FormLabel>
                            <FormControl>
                                <Select
                                    value={field.value || ''}
                                    onValueChange={field.onChange}
                                >
                                    <SelectTrigger className="border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-900">
                                        <SelectValue placeholder="Selecione uma modalidade" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {modalities.map((modality) => (
                                            <SelectItem key={modality.id} value={modality.id}>
                                                {modality.name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </FormControl>
                            <FormMessage />
                        </FormItem>
                    )}
                />

                {/* Seleção de Faixa */}
                {selectedModalityId && (
                    <FormField
                        control={control}
                        name="selected_belt_level_id"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                    Faixa Inicial*
                                </FormLabel>
                                <FormControl>
                                    {isLoadingBelts ? (
                                        <div className="flex items-center justify-center py-4 border border-slate-200 dark:border-slate-700 rounded-md bg-slate-50 dark:bg-slate-900">
                                            <Loader2 className="w-4 h-4 animate-spin text-slate-400" />
                                            <span className="ml-2 text-sm text-slate-600 dark:text-slate-400">
                                                Carregando faixas...
                                            </span>
                                        </div>
                                    ) : beltLevels.length === 0 ? (
                                        <div className="flex items-center justify-center py-4 border border-slate-200 dark:border-slate-700 rounded-md bg-slate-50 dark:bg-slate-900">
                                            <span className="text-sm text-slate-600 dark:text-slate-400">
                                                Nenhuma faixa configurada para esta modalidade
                                            </span>
                                        </div>
                                    ) : (
                                        <Select
                                            value={field.value || ''}
                                            onValueChange={field.onChange}
                                        >
                                            <SelectTrigger className="border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-900 min-h-[48px]">
                                                <SelectValue>
                                                    {(() => {
                                                        const selectedBelt = beltLevels.find(belt => belt.id === selectedBeltLevelId);
                                                        if (!selectedBelt) return "Selecione uma faixa";
                                                        return (
                                                            <div className="flex items-center">
                                                                {renderBeltVisual(selectedBelt, 'xs')}
                                                            </div>
                                                        );
                                                    })()}
                                                </SelectValue>
                                            </SelectTrigger>
                                            <SelectContent className="max-h-[300px]">
                                                {beltLevels.map((belt) => (
                                                    <SelectItem
                                                        key={belt.id}
                                                        value={belt.id}
                                                        className="py-3 cursor-pointer"
                                                    >
                                                        {renderBeltVisual(belt)}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    )}
                                </FormControl>
                                <FormMessage />

                                {/* Informação adicional sobre a faixa selecionada */}
                                {selectedBeltLevelId && beltLevels.length > 0 && (
                                    <div className="mt-3 p-3 bg-slate-50 dark:bg-slate-800 rounded-md border border-slate-200 dark:border-slate-700">
                                        <div className="flex items-center gap-3">
                                            <div className="flex-shrink-0">
                                                {(() => {
                                                    const selectedBelt = beltLevels.find(belt => belt.id === selectedBeltLevelId);
                                                    return selectedBelt ? renderBeltVisual(selectedBelt) : null;
                                                })()}
                                            </div>
                                            <div className="text-xs text-slate-600 dark:text-slate-400">
                                                Faixa inicial selecionada para o novo aluno
                                            </div>
                                        </div>
                                    </div>
                                )}
                            </FormItem>
                        )}
                    />
                )}

                {modalities.length === 0 && (
                    <div className="text-center py-8">
                        <p className="text-slate-600 dark:text-slate-400">
                            Nenhuma modalidade ativa encontrada.
                        </p>
                        <p className="text-sm text-slate-500 dark:text-slate-500 mt-1">
                            Configure modalidades na aba de configurações da academia para poder definir faixas.
                        </p>
                    </div>
                )}
            </CardContent>
        </Card>
    );
}